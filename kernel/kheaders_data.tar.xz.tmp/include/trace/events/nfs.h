/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Display helpers for NFS protocol elements
 *
 * Author: <PERSON> <<EMAIL>>
 *
 * Copyright (c) 2020, Oracle and/or its affiliates.
 */

#include <linux/nfs.h>
#include <linux/nfs4.h>
#include <uapi/linux/nfs.h>

TRACE_DEFINE_ENUM(NFS_OK);
TRACE_DEFINE_ENUM(NFSERR_PERM);
TRACE_DEFINE_ENUM(NFSERR_NOENT);
TRACE_DEFINE_ENUM(NFSERR_IO);
TRACE_DEFINE_ENUM(NFSERR_NXIO);
TRACE_DEFINE_ENUM(NFSERR_EAGAIN);
TRACE_DEFINE_ENUM(NFSERR_ACCES);
TRACE_DEFINE_ENUM(NFSERR_EXIST);
TRACE_DEFINE_ENUM(NFSERR_XDEV);
TRACE_DEFINE_ENUM(NFSERR_NODEV);
TRACE_DEFINE_ENUM(NFSERR_NOTDIR);
TRACE_DEFINE_ENUM(NFSERR_ISDIR);
TRACE_DEFINE_ENUM(NFSERR_INVAL);
TRACE_DEFINE_ENUM(NFSERR_FBIG);
TRACE_DEFINE_ENUM(NFSERR_NOSPC);
TRACE_DEFINE_ENUM(NFSERR_ROFS);
TRACE_DEFINE_ENUM(NFSERR_MLINK);
TRACE_DEFINE_ENUM(NFSERR_OPNOTSUPP);
TRACE_DEFINE_ENUM(NFSERR_NAMETOOLONG);
TRACE_DEFINE_ENUM(NFSERR_NOTEMPTY);
TRACE_DEFINE_ENUM(NFSERR_DQUOT);
TRACE_DEFINE_ENUM(NFSERR_STALE);
TRACE_DEFINE_ENUM(NFSERR_REMOTE);
TRACE_DEFINE_ENUM(NFSERR_WFLUSH);
TRACE_DEFINE_ENUM(NFSERR_BADHANDLE);
TRACE_DEFINE_ENUM(NFSERR_NOT_SYNC);
TRACE_DEFINE_ENUM(NFSERR_BAD_COOKIE);
TRACE_DEFINE_ENUM(NFSERR_NOTSUPP);
TRACE_DEFINE_ENUM(NFSERR_TOOSMALL);
TRACE_DEFINE_ENUM(NFSERR_SERVERFAULT);
TRACE_DEFINE_ENUM(NFSERR_BADTYPE);
TRACE_DEFINE_ENUM(NFSERR_JUKEBOX);

#define show_nfs_status(x) \
	__print_symbolic(x, \
		{ NFS_OK,			"OK" }, \
		{ NFSERR_PERM,			"PERM" }, \
		{ NFSERR_NOENT,			"NOENT" }, \
		{ NFSERR_IO,			"IO" }, \
		{ NFSERR_NXIO,			"NXIO" }, \
		{ ECHILD,			"CHILD" }, \
		{ NFSERR_EAGAIN,		"AGAIN" }, \
		{ NFSERR_ACCES,			"ACCES" }, \
		{ NFSERR_EXIST,			"EXIST" }, \
		{ NFSERR_XDEV,			"XDEV" }, \
		{ NFSERR_NODEV,			"NODEV" }, \
		{ NFSERR_NOTDIR,		"NOTDIR" }, \
		{ NFSERR_ISDIR,			"ISDIR" }, \
		{ NFSERR_INVAL,			"INVAL" }, \
		{ NFSERR_FBIG,			"FBIG" }, \
		{ NFSERR_NOSPC,			"NOSPC" }, \
		{ NFSERR_ROFS,			"ROFS" }, \
		{ NFSERR_MLINK,			"MLINK" }, \
		{ NFSERR_OPNOTSUPP,		"OPNOTSUPP" }, \
		{ NFSERR_NAMETOOLONG,		"NAMETOOLONG" }, \
		{ NFSERR_NOTEMPTY,		"NOTEMPTY" }, \
		{ NFSERR_DQUOT,			"DQUOT" }, \
		{ NFSERR_STALE,			"STALE" }, \
		{ NFSERR_REMOTE,		"REMOTE" }, \
		{ NFSERR_WFLUSH,		"WFLUSH" }, \
		{ NFSERR_BADHANDLE,		"BADHANDLE" }, \
		{ NFSERR_NOT_SYNC,		"NOTSYNC" }, \
		{ NFSERR_BAD_COOKIE,		"BADCOOKIE" }, \
		{ NFSERR_NOTSUPP,		"NOTSUPP" }, \
		{ NFSERR_TOOSMALL,		"TOOSMALL" }, \
		{ NFSERR_SERVERFAULT,		"REMOTEIO" }, \
		{ NFSERR_BADTYPE,		"BADTYPE" }, \
		{ NFSERR_JUKEBOX,		"JUKEBOX" })

TRACE_DEFINE_ENUM(NFS_UNSTABLE);
TRACE_DEFINE_ENUM(NFS_DATA_SYNC);
TRACE_DEFINE_ENUM(NFS_FILE_SYNC);

#define show_nfs_stable_how(x) \
	__print_symbolic(x, \
		{ NFS_UNSTABLE,			"UNSTABLE" }, \
		{ NFS_DATA_SYNC,		"DATA_SYNC" }, \
		{ NFS_FILE_SYNC,		"FILE_SYNC" })

TRACE_DEFINE_ENUM(NFS4_OK);
TRACE_DEFINE_ENUM(NFS4ERR_ACCESS);
TRACE_DEFINE_ENUM(NFS4ERR_ATTRNOTSUPP);
TRACE_DEFINE_ENUM(NFS4ERR_ADMIN_REVOKED);
TRACE_DEFINE_ENUM(NFS4ERR_BACK_CHAN_BUSY);
TRACE_DEFINE_ENUM(NFS4ERR_BADCHAR);
TRACE_DEFINE_ENUM(NFS4ERR_BADHANDLE);
TRACE_DEFINE_ENUM(NFS4ERR_BADIOMODE);
TRACE_DEFINE_ENUM(NFS4ERR_BADLAYOUT);
TRACE_DEFINE_ENUM(NFS4ERR_BADLABEL);
TRACE_DEFINE_ENUM(NFS4ERR_BADNAME);
TRACE_DEFINE_ENUM(NFS4ERR_BADOWNER);
TRACE_DEFINE_ENUM(NFS4ERR_BADSESSION);
TRACE_DEFINE_ENUM(NFS4ERR_BADSLOT);
TRACE_DEFINE_ENUM(NFS4ERR_BADTYPE);
TRACE_DEFINE_ENUM(NFS4ERR_BADXDR);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_COOKIE);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_HIGH_SLOT);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_RANGE);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_SEQID);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_SESSION_DIGEST);
TRACE_DEFINE_ENUM(NFS4ERR_BAD_STATEID);
TRACE_DEFINE_ENUM(NFS4ERR_CB_PATH_DOWN);
TRACE_DEFINE_ENUM(NFS4ERR_CLID_INUSE);
TRACE_DEFINE_ENUM(NFS4ERR_CLIENTID_BUSY);
TRACE_DEFINE_ENUM(NFS4ERR_COMPLETE_ALREADY);
TRACE_DEFINE_ENUM(NFS4ERR_CONN_NOT_BOUND_TO_SESSION);
TRACE_DEFINE_ENUM(NFS4ERR_DEADLOCK);
TRACE_DEFINE_ENUM(NFS4ERR_DEADSESSION);
TRACE_DEFINE_ENUM(NFS4ERR_DELAY);
TRACE_DEFINE_ENUM(NFS4ERR_DELEG_ALREADY_WANTED);
TRACE_DEFINE_ENUM(NFS4ERR_DELEG_REVOKED);
TRACE_DEFINE_ENUM(NFS4ERR_DENIED);
TRACE_DEFINE_ENUM(NFS4ERR_DIRDELEG_UNAVAIL);
TRACE_DEFINE_ENUM(NFS4ERR_DQUOT);
TRACE_DEFINE_ENUM(NFS4ERR_ENCR_ALG_UNSUPP);
TRACE_DEFINE_ENUM(NFS4ERR_EXIST);
TRACE_DEFINE_ENUM(NFS4ERR_EXPIRED);
TRACE_DEFINE_ENUM(NFS4ERR_FBIG);
TRACE_DEFINE_ENUM(NFS4ERR_FHEXPIRED);
TRACE_DEFINE_ENUM(NFS4ERR_FILE_OPEN);
TRACE_DEFINE_ENUM(NFS4ERR_GRACE);
TRACE_DEFINE_ENUM(NFS4ERR_HASH_ALG_UNSUPP);
TRACE_DEFINE_ENUM(NFS4ERR_INVAL);
TRACE_DEFINE_ENUM(NFS4ERR_IO);
TRACE_DEFINE_ENUM(NFS4ERR_ISDIR);
TRACE_DEFINE_ENUM(NFS4ERR_LAYOUTTRYLATER);
TRACE_DEFINE_ENUM(NFS4ERR_LAYOUTUNAVAILABLE);
TRACE_DEFINE_ENUM(NFS4ERR_LEASE_MOVED);
TRACE_DEFINE_ENUM(NFS4ERR_LOCKED);
TRACE_DEFINE_ENUM(NFS4ERR_LOCKS_HELD);
TRACE_DEFINE_ENUM(NFS4ERR_LOCK_RANGE);
TRACE_DEFINE_ENUM(NFS4ERR_MINOR_VERS_MISMATCH);
TRACE_DEFINE_ENUM(NFS4ERR_MLINK);
TRACE_DEFINE_ENUM(NFS4ERR_MOVED);
TRACE_DEFINE_ENUM(NFS4ERR_NAMETOOLONG);
TRACE_DEFINE_ENUM(NFS4ERR_NOENT);
TRACE_DEFINE_ENUM(NFS4ERR_NOFILEHANDLE);
TRACE_DEFINE_ENUM(NFS4ERR_NOMATCHING_LAYOUT);
TRACE_DEFINE_ENUM(NFS4ERR_NOSPC);
TRACE_DEFINE_ENUM(NFS4ERR_NOTDIR);
TRACE_DEFINE_ENUM(NFS4ERR_NOTEMPTY);
TRACE_DEFINE_ENUM(NFS4ERR_NOTSUPP);
TRACE_DEFINE_ENUM(NFS4ERR_NOT_ONLY_OP);
TRACE_DEFINE_ENUM(NFS4ERR_NOT_SAME);
TRACE_DEFINE_ENUM(NFS4ERR_NO_GRACE);
TRACE_DEFINE_ENUM(NFS4ERR_NXIO);
TRACE_DEFINE_ENUM(NFS4ERR_OLD_STATEID);
TRACE_DEFINE_ENUM(NFS4ERR_OPENMODE);
TRACE_DEFINE_ENUM(NFS4ERR_OP_ILLEGAL);
TRACE_DEFINE_ENUM(NFS4ERR_OP_NOT_IN_SESSION);
TRACE_DEFINE_ENUM(NFS4ERR_PERM);
TRACE_DEFINE_ENUM(NFS4ERR_PNFS_IO_HOLE);
TRACE_DEFINE_ENUM(NFS4ERR_PNFS_NO_LAYOUT);
TRACE_DEFINE_ENUM(NFS4ERR_RECALLCONFLICT);
TRACE_DEFINE_ENUM(NFS4ERR_RECLAIM_BAD);
TRACE_DEFINE_ENUM(NFS4ERR_RECLAIM_CONFLICT);
TRACE_DEFINE_ENUM(NFS4ERR_REJECT_DELEG);
TRACE_DEFINE_ENUM(NFS4ERR_REP_TOO_BIG);
TRACE_DEFINE_ENUM(NFS4ERR_REP_TOO_BIG_TO_CACHE);
TRACE_DEFINE_ENUM(NFS4ERR_REQ_TOO_BIG);
TRACE_DEFINE_ENUM(NFS4ERR_RESOURCE);
TRACE_DEFINE_ENUM(NFS4ERR_RESTOREFH);
TRACE_DEFINE_ENUM(NFS4ERR_RETRY_UNCACHED_REP);
TRACE_DEFINE_ENUM(NFS4ERR_RETURNCONFLICT);
TRACE_DEFINE_ENUM(NFS4ERR_ROFS);
TRACE_DEFINE_ENUM(NFS4ERR_SAME);
TRACE_DEFINE_ENUM(NFS4ERR_SHARE_DENIED);
TRACE_DEFINE_ENUM(NFS4ERR_SEQUENCE_POS);
TRACE_DEFINE_ENUM(NFS4ERR_SEQ_FALSE_RETRY);
TRACE_DEFINE_ENUM(NFS4ERR_SEQ_MISORDERED);
TRACE_DEFINE_ENUM(NFS4ERR_SERVERFAULT);
TRACE_DEFINE_ENUM(NFS4ERR_STALE);
TRACE_DEFINE_ENUM(NFS4ERR_STALE_CLIENTID);
TRACE_DEFINE_ENUM(NFS4ERR_STALE_STATEID);
TRACE_DEFINE_ENUM(NFS4ERR_SYMLINK);
TRACE_DEFINE_ENUM(NFS4ERR_TOOSMALL);
TRACE_DEFINE_ENUM(NFS4ERR_TOO_MANY_OPS);
TRACE_DEFINE_ENUM(NFS4ERR_UNKNOWN_LAYOUTTYPE);
TRACE_DEFINE_ENUM(NFS4ERR_UNSAFE_COMPOUND);
TRACE_DEFINE_ENUM(NFS4ERR_WRONGSEC);
TRACE_DEFINE_ENUM(NFS4ERR_WRONG_CRED);
TRACE_DEFINE_ENUM(NFS4ERR_WRONG_TYPE);
TRACE_DEFINE_ENUM(NFS4ERR_XDEV);

TRACE_DEFINE_ENUM(NFS4ERR_RESET_TO_MDS);
TRACE_DEFINE_ENUM(NFS4ERR_RESET_TO_PNFS);

#define show_nfs4_status(x) \
	__print_symbolic(x, \
		{ NFS4_OK,			"OK" }, \
		{ EPERM,			"EPERM" }, \
		{ ENOENT,			"ENOENT" }, \
		{ EIO,				"EIO" }, \
		{ ENXIO,			"ENXIO" }, \
		{ EACCES,			"EACCES" }, \
		{ EEXIST,			"EEXIST" }, \
		{ EXDEV,			"EXDEV" }, \
		{ ENOTDIR,			"ENOTDIR" }, \
		{ EISDIR,			"EISDIR" }, \
		{ EFBIG,			"EFBIG" }, \
		{ ENOSPC,			"ENOSPC" }, \
		{ EROFS,			"EROFS" }, \
		{ EMLINK,			"EMLINK" }, \
		{ ENAMETOOLONG,			"ENAMETOOLONG" }, \
		{ ENOTEMPTY,			"ENOTEMPTY" }, \
		{ EDQUOT,			"EDQUOT" }, \
		{ ESTALE,			"ESTALE" }, \
		{ EBADHANDLE,			"EBADHANDLE" }, \
		{ EBADCOOKIE,			"EBADCOOKIE" }, \
		{ ENOTSUPP,			"ENOTSUPP" }, \
		{ ETOOSMALL,			"ETOOSMALL" }, \
		{ EREMOTEIO,			"EREMOTEIO" }, \
		{ EBADTYPE,			"EBADTYPE" }, \
		{ EAGAIN,			"EAGAIN" }, \
		{ ELOOP,			"ELOOP" }, \
		{ EOPNOTSUPP,			"EOPNOTSUPP" }, \
		{ EDEADLK,			"EDEADLK" }, \
		{ ENOMEM,			"ENOMEM" }, \
		{ EKEYEXPIRED,			"EKEYEXPIRED" }, \
		{ ETIMEDOUT,			"ETIMEDOUT" }, \
		{ ERESTARTSYS,			"ERESTARTSYS" }, \
		{ ECONNREFUSED,			"ECONNREFUSED" }, \
		{ ECONNRESET,			"ECONNRESET" }, \
		{ ENETUNREACH,			"ENETUNREACH" }, \
		{ EHOSTUNREACH,			"EHOSTUNREACH" }, \
		{ EHOSTDOWN,			"EHOSTDOWN" }, \
		{ EPIPE,			"EPIPE" }, \
		{ EPFNOSUPPORT,			"EPFNOSUPPORT" }, \
		{ EPROTONOSUPPORT,		"EPROTONOSUPPORT" }, \
		{ NFS4ERR_ACCESS,		"ACCESS" }, \
		{ NFS4ERR_ATTRNOTSUPP,		"ATTRNOTSUPP" }, \
		{ NFS4ERR_ADMIN_REVOKED,	"ADMIN_REVOKED" }, \
		{ NFS4ERR_BACK_CHAN_BUSY,	"BACK_CHAN_BUSY" }, \
		{ NFS4ERR_BADCHAR,		"BADCHAR" }, \
		{ NFS4ERR_BADHANDLE,		"BADHANDLE" }, \
		{ NFS4ERR_BADIOMODE,		"BADIOMODE" }, \
		{ NFS4ERR_BADLAYOUT,		"BADLAYOUT" }, \
		{ NFS4ERR_BADLABEL,		"BADLABEL" }, \
		{ NFS4ERR_BADNAME,		"BADNAME" }, \
		{ NFS4ERR_BADOWNER,		"BADOWNER" }, \
		{ NFS4ERR_BADSESSION,		"BADSESSION" }, \
		{ NFS4ERR_BADSLOT,		"BADSLOT" }, \
		{ NFS4ERR_BADTYPE,		"BADTYPE" }, \
		{ NFS4ERR_BADXDR,		"BADXDR" }, \
		{ NFS4ERR_BAD_COOKIE,		"BAD_COOKIE" }, \
		{ NFS4ERR_BAD_HIGH_SLOT,	"BAD_HIGH_SLOT" }, \
		{ NFS4ERR_BAD_RANGE,		"BAD_RANGE" }, \
		{ NFS4ERR_BAD_SEQID,		"BAD_SEQID" }, \
		{ NFS4ERR_BAD_SESSION_DIGEST,	"BAD_SESSION_DIGEST" }, \
		{ NFS4ERR_BAD_STATEID,		"BAD_STATEID" }, \
		{ NFS4ERR_CB_PATH_DOWN,		"CB_PATH_DOWN" }, \
		{ NFS4ERR_CLID_INUSE,		"CLID_INUSE" }, \
		{ NFS4ERR_CLIENTID_BUSY,	"CLIENTID_BUSY" }, \
		{ NFS4ERR_COMPLETE_ALREADY,	"COMPLETE_ALREADY" }, \
		{ NFS4ERR_CONN_NOT_BOUND_TO_SESSION, "CONN_NOT_BOUND_TO_SESSION" }, \
		{ NFS4ERR_DEADLOCK,		"DEADLOCK" }, \
		{ NFS4ERR_DEADSESSION,		"DEAD_SESSION" }, \
		{ NFS4ERR_DELAY,		"DELAY" }, \
		{ NFS4ERR_DELEG_ALREADY_WANTED,	"DELEG_ALREADY_WANTED" }, \
		{ NFS4ERR_DELEG_REVOKED,	"DELEG_REVOKED" }, \
		{ NFS4ERR_DENIED,		"DENIED" }, \
		{ NFS4ERR_DIRDELEG_UNAVAIL,	"DIRDELEG_UNAVAIL" }, \
		{ NFS4ERR_DQUOT,		"DQUOT" }, \
		{ NFS4ERR_ENCR_ALG_UNSUPP,	"ENCR_ALG_UNSUPP" }, \
		{ NFS4ERR_EXIST,		"EXIST" }, \
		{ NFS4ERR_EXPIRED,		"EXPIRED" }, \
		{ NFS4ERR_FBIG,			"FBIG" }, \
		{ NFS4ERR_FHEXPIRED,		"FHEXPIRED" }, \
		{ NFS4ERR_FILE_OPEN,		"FILE_OPEN" }, \
		{ NFS4ERR_GRACE,		"GRACE" }, \
		{ NFS4ERR_HASH_ALG_UNSUPP,	"HASH_ALG_UNSUPP" }, \
		{ NFS4ERR_INVAL,		"INVAL" }, \
		{ NFS4ERR_IO,			"IO" }, \
		{ NFS4ERR_ISDIR,		"ISDIR" }, \
		{ NFS4ERR_LAYOUTTRYLATER,	"LAYOUTTRYLATER" }, \
		{ NFS4ERR_LAYOUTUNAVAILABLE,	"LAYOUTUNAVAILABLE" }, \
		{ NFS4ERR_LEASE_MOVED,		"LEASE_MOVED" }, \
		{ NFS4ERR_LOCKED,		"LOCKED" }, \
		{ NFS4ERR_LOCKS_HELD,		"LOCKS_HELD" }, \
		{ NFS4ERR_LOCK_RANGE,		"LOCK_RANGE" }, \
		{ NFS4ERR_MINOR_VERS_MISMATCH,	"MINOR_VERS_MISMATCH" }, \
		{ NFS4ERR_MLINK,		"MLINK" }, \
		{ NFS4ERR_MOVED,		"MOVED" }, \
		{ NFS4ERR_NAMETOOLONG,		"NAMETOOLONG" }, \
		{ NFS4ERR_NOENT,		"NOENT" }, \
		{ NFS4ERR_NOFILEHANDLE,		"NOFILEHANDLE" }, \
		{ NFS4ERR_NOMATCHING_LAYOUT,	"NOMATCHING_LAYOUT" }, \
		{ NFS4ERR_NOSPC,		"NOSPC" }, \
		{ NFS4ERR_NOTDIR,		"NOTDIR" }, \
		{ NFS4ERR_NOTEMPTY,		"NOTEMPTY" }, \
		{ NFS4ERR_NOTSUPP,		"NOTSUPP" }, \
		{ NFS4ERR_NOT_ONLY_OP,		"NOT_ONLY_OP" }, \
		{ NFS4ERR_NOT_SAME,		"NOT_SAME" }, \
		{ NFS4ERR_NO_GRACE,		"NO_GRACE" }, \
		{ NFS4ERR_NXIO,			"NXIO" }, \
		{ NFS4ERR_OLD_STATEID,		"OLD_STATEID" }, \
		{ NFS4ERR_OPENMODE,		"OPENMODE" }, \
		{ NFS4ERR_OP_ILLEGAL,		"OP_ILLEGAL" }, \
		{ NFS4ERR_OP_NOT_IN_SESSION,	"OP_NOT_IN_SESSION" }, \
		{ NFS4ERR_PERM,			"PERM" }, \
		{ NFS4ERR_PNFS_IO_HOLE,		"PNFS_IO_HOLE" }, \
		{ NFS4ERR_PNFS_NO_LAYOUT,	"PNFS_NO_LAYOUT" }, \
		{ NFS4ERR_RECALLCONFLICT,	"RECALLCONFLICT" }, \
		{ NFS4ERR_RECLAIM_BAD,		"RECLAIM_BAD" }, \
		{ NFS4ERR_RECLAIM_CONFLICT,	"RECLAIM_CONFLICT" }, \
		{ NFS4ERR_REJECT_DELEG,		"REJECT_DELEG" }, \
		{ NFS4ERR_REP_TOO_BIG,		"REP_TOO_BIG" }, \
		{ NFS4ERR_REP_TOO_BIG_TO_CACHE,	"REP_TOO_BIG_TO_CACHE" }, \
		{ NFS4ERR_REQ_TOO_BIG,		"REQ_TOO_BIG" }, \
		{ NFS4ERR_RESOURCE,		"RESOURCE" }, \
		{ NFS4ERR_RESTOREFH,		"RESTOREFH" }, \
		{ NFS4ERR_RETRY_UNCACHED_REP,	"RETRY_UNCACHED_REP" }, \
		{ NFS4ERR_RETURNCONFLICT,	"RETURNCONFLICT" }, \
		{ NFS4ERR_ROFS,			"ROFS" }, \
		{ NFS4ERR_SAME,			"SAME" }, \
		{ NFS4ERR_SHARE_DENIED,		"SHARE_DENIED" }, \
		{ NFS4ERR_SEQUENCE_POS,		"SEQUENCE_POS" }, \
		{ NFS4ERR_SEQ_FALSE_RETRY,	"SEQ_FALSE_RETRY" }, \
		{ NFS4ERR_SEQ_MISORDERED,	"SEQ_MISORDERED" }, \
		{ NFS4ERR_SERVERFAULT,		"SERVERFAULT" }, \
		{ NFS4ERR_STALE,		"STALE" }, \
		{ NFS4ERR_STALE_CLIENTID,	"STALE_CLIENTID" }, \
		{ NFS4ERR_STALE_STATEID,	"STALE_STATEID" }, \
		{ NFS4ERR_SYMLINK,		"SYMLINK" }, \
		{ NFS4ERR_TOOSMALL,		"TOOSMALL" }, \
		{ NFS4ERR_TOO_MANY_OPS,		"TOO_MANY_OPS" }, \
		{ NFS4ERR_UNKNOWN_LAYOUTTYPE,	"UNKNOWN_LAYOUTTYPE" }, \
		{ NFS4ERR_UNSAFE_COMPOUND,	"UNSAFE_COMPOUND" }, \
		{ NFS4ERR_WRONGSEC,		"WRONGSEC" }, \
		{ NFS4ERR_WRONG_CRED,		"WRONG_CRED" }, \
		{ NFS4ERR_WRONG_TYPE,		"WRONG_TYPE" }, \
		{ NFS4ERR_XDEV,			"XDEV" }, \
		/* ***** Internal to Linux NFS client ***** */ \
		{ NFS4ERR_RESET_TO_MDS,		"RESET_TO_MDS" }, \
		{ NFS4ERR_RESET_TO_PNFS,	"RESET_TO_PNFS" })

#define show_nfs4_verifier(x) \
	__print_hex_str(x, NFS4_VERIFIER_SIZE)

TRACE_DEFINE_ENUM(IOMODE_READ);
TRACE_DEFINE_ENUM(IOMODE_RW);
TRACE_DEFINE_ENUM(IOMODE_ANY);

#define show_pnfs_layout_iomode(x) \
	__print_symbolic(x, \
		{ IOMODE_READ,			"READ" }, \
		{ IOMODE_RW,			"RW" }, \
		{ IOMODE_ANY,			"ANY" })

#define show_nfs4_seq4_status(x) \
	__print_flags(x, "|", \
		{ SEQ4_STATUS_CB_PATH_DOWN,		"CB_PATH_DOWN" }, \
		{ SEQ4_STATUS_CB_GSS_CONTEXTS_EXPIRING,	"CB_GSS_CONTEXTS_EXPIRING" }, \
		{ SEQ4_STATUS_CB_GSS_CONTEXTS_EXPIRED,	"CB_GSS_CONTEXTS_EXPIRED" }, \
		{ SEQ4_STATUS_EXPIRED_ALL_STATE_REVOKED, "EXPIRED_ALL_STATE_REVOKED" }, \
		{ SEQ4_STATUS_EXPIRED_SOME_STATE_REVOKED, "EXPIRED_SOME_STATE_REVOKED" }, \
		{ SEQ4_STATUS_ADMIN_STATE_REVOKED,	"ADMIN_STATE_REVOKED" }, \
		{ SEQ4_STATUS_RECALLABLE_STATE_REVOKED,	"RECALLABLE_STATE_REVOKED" }, \
		{ SEQ4_STATUS_LEASE_MOVED,		"LEASE_MOVED" }, \
		{ SEQ4_STATUS_RESTART_RECLAIM_NEEDED,	"RESTART_RECLAIM_NEEDED" }, \
		{ SEQ4_STATUS_CB_PATH_DOWN_SESSION,	"CB_PATH_DOWN_SESSION" }, \
		{ SEQ4_STATUS_BACKCHANNEL_FAULT,	"BACKCHANNEL_FAULT" })
