/* SPDX-License-Identifier: (GPL-2.0+ OR MIT)
 *
 * Copyright (c) 2018 Icenowy Zheng <<EMAIL>>
 *
 */

#ifndef _DT_BINDINGS_CLK_SUNIV_F1C100S_H_
#define _DT_BINDINGS_CLK_SUNIV_F1C100S_H_

#define CLK_CPU			11

#define CLK_BUS_DMA		14
#define CLK_BUS_MMC0		15
#define CLK_BUS_MMC1		16
#define CLK_BUS_DRAM		17
#define CLK_BUS_SPI0		18
#define CLK_BUS_SPI1		19
#define CLK_BUS_OTG		20
#define CLK_BUS_VE		21
#define CLK_BUS_LCD		22
#define CLK_BUS_DEINTERLACE	23
#define CLK_BUS_CSI		24
#define CLK_BUS_TVD		25
#define CLK_BUS_TVE		26
#define CLK_BUS_DE_BE		27
#define CLK_BUS_DE_FE		28
#define CLK_BUS_CODEC		29
#define CLK_BUS_SPDIF		30
#define CLK_BUS_IR		31
#define CLK_BUS_RSB		32
#define CLK_BUS_I2S0		33
#define CLK_BUS_I2C0		34
#define CLK_BUS_I2C1		35
#define CLK_BUS_I2C2		36
#define CLK_BUS_PIO		37
#define CLK_BUS_UART0		38
#define CLK_BUS_UART1		39
#define CLK_BUS_UART2		40

#define CLK_MMC0		41
#define CLK_MMC0_SAMPLE		42
#define CLK_MMC0_OUTPUT		43
#define CLK_MMC1		44
#define CLK_MMC1_SAMPLE		45
#define CLK_MMC1_OUTPUT		46
#define CLK_I2S			47
#define CLK_SPDIF		48

#define CLK_USB_PHY0		49

#define CLK_DRAM_VE		50
#define CLK_DRAM_CSI		51
#define CLK_DRAM_DEINTERLACE	52
#define CLK_DRAM_TVD		53
#define CLK_DRAM_DE_FE		54
#define CLK_DRAM_DE_BE		55

#define CLK_DE_BE		56
#define CLK_DE_FE		57
#define CLK_TCON		58
#define CLK_DEINTERLACE		59
#define CLK_TVE2_CLK		60
#define CLK_TVE1_CLK		61
#define CLK_TVD			62
#define CLK_CSI			63
#define CLK_VE			64
#define CLK_CODEC		65
#define CLK_AVS			66

#endif
