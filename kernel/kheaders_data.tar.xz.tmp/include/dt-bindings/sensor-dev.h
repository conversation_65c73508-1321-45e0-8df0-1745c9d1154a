/* SPDX-License-Identifier: GPL-2.0 */

#ifndef __DT_BINDINGS_ROCKCHIP_SENSORDEV_H__
#define __DT_BINDINGS_ROCKCHIP_SENSORDEV_H__

#define	SENSOR_TYPE_NULL  0	
#define	SENSOR_TYPE_ANGLE 1
#define	SENSOR_TYPE_ACCEL 2
#define	SENSOR_TYPE_COMPASS 3	
#define	SENSOR_TYPE_GYROSCOPE 4	
#define	SENSOR_TYPE_LIGHT 5	
#define	SENSOR_TYPE_PROXIMITY 6
#define	SENSOR_TYPE_TEMPERATURE 7	
#define	SENSOR_TYPE_PRESSURE 8
#define	SENSOR_TYPE_HALL 9
#define	SENSOR_NUM_TYPES 10

#endif
