/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA114_MC_H
#define DT_BINDINGS_MEMORY_TEGRA114_MC_H

#define TEGRA_SWGROUP_PTC	0
#define TEGRA_SWGROUP_DC	1
#define TEGRA_SWGROUP_DCB	2
#define TEGRA_SWGROUP_EPP	3
#define TEGRA_SWGROUP_G2	4
#define TEGRA_SWGROUP_AVPC	5
#define TEGRA_SWGROUP_NV	6
#define TEGRA_SWGROUP_HDA	7
#define TEGRA_SWGROUP_HC	8
#define TEGRA_SWGROUP_MSENC	9
#define TEGRA_SWGROUP_PPCS	10
#define TEGRA_SWGROUP_VDE	11
#define TEGRA_SWGROUP_MPCORELP	12
#define TEGRA_SWGROUP_MPCORE	13
#define TEGRA_SWGROUP_VI	14
#define TEGRA_SWGROUP_ISP	15
#define TEGRA_SWGROUP_XUSB_HOST	16
#define TEGRA_SWGROUP_XUSB_DEV	17
#define TEGRA_SWGROUP_EMUCIF	18
#define TEGRA_SWGROUP_TSEC	19

#define TEGRA114_MC_RESET_AVPC		0
#define TEGRA114_MC_RESET_DC		1
#define TEGRA114_MC_RESET_DCB		2
#define TEGRA114_MC_RESET_EPP		3
#define TEGRA114_MC_RESET_2D		4
#define TEGRA114_MC_RESET_HC		5
#define TEGRA114_MC_RESET_HDA		6
#define TEGRA114_MC_RESET_ISP		7
#define TEGRA114_MC_RESET_MPCORE	8
#define TEGRA114_MC_RESET_MPCORELP	9
#define TEGRA114_MC_RESET_MPE		10
#define TEGRA114_MC_RESET_3D		11
#define TEGRA114_MC_RESET_3D2		12
#define TEGRA114_MC_RESET_PPCS		13
#define TEGRA114_MC_RESET_VDE		14
#define TEGRA114_MC_RESET_VI		15

#endif
