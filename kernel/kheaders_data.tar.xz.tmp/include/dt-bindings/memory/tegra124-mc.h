/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA124_MC_H
#define DT_BINDINGS_MEMORY_TEGRA124_MC_H

#define TEGRA_SWGROUP_PTC	0
#define TEGRA_SWGROUP_DC	1
#define TEGRA_SWGROUP_DCB	2
#define TEGRA_SWGROUP_AFI	3
#define TEGRA_SWGROUP_AVPC	4
#define TEGRA_SWGROUP_HDA	5
#define TEGRA_SWGROUP_HC	6
#define TEGRA_SWGROUP_MSENC	7
#define TEGRA_SWGROUP_PPCS	8
#define TEGRA_SWGROUP_SATA	9
#define TEGRA_SWGROUP_VDE	10
#define TEGRA_SWGROUP_MPCORELP	11
#define TEGRA_SWGROUP_MPCORE	12
#define TEGRA_SWGROUP_ISP2	13
#define TEGRA_SWGROUP_XUSB_HOST	14
#define TEGRA_SWGROUP_XUSB_DEV	15
#define TEGRA_SWGROUP_ISP2B	16
#define TEGRA_SWGROUP_TSEC	17
#define TEGRA_SWGROUP_A9AVP	18
#define TEGRA_SWGROUP_GPU	19
#define TEGRA_SWGROUP_SDMMC1A	20
#define TEGRA_SWGROUP_SDMMC2A	21
#define TEGRA_SWGROUP_SDMMC3A	22
#define TEGRA_SWGROUP_SDMMC4A	23
#define TEGRA_SWGROUP_VIC	24
#define TEGRA_SWGROUP_VI	25

#define TEGRA124_MC_RESET_AFI		0
#define TEGRA124_MC_RESET_AVPC		1
#define TEGRA124_MC_RESET_DC		2
#define TEGRA124_MC_RESET_DCB		3
#define TEGRA124_MC_RESET_HC		4
#define TEGRA124_MC_RESET_HDA		5
#define TEGRA124_MC_RESET_ISP2		6
#define TEGRA124_MC_RESET_MPCORE	7
#define TEGRA124_MC_RESET_MPCORELP	8
#define TEGRA124_MC_RESET_MSENC		9
#define TEGRA124_MC_RESET_PPCS		10
#define TEGRA124_MC_RESET_SATA		11
#define TEGRA124_MC_RESET_VDE		12
#define TEGRA124_MC_RESET_VI		13
#define TEGRA124_MC_RESET_VIC		14
#define TEGRA124_MC_RESET_XUSB_HOST	15
#define TEGRA124_MC_RESET_XUSB_DEV	16
#define TEGRA124_MC_RESET_TSEC		17
#define TEGRA124_MC_RESET_SDMMC1	18
#define TEGRA124_MC_RESET_SDMMC2	19
#define TEGRA124_MC_RESET_SDMMC3	20
#define TEGRA124_MC_RESET_SDMMC4	21
#define TEGRA124_MC_RESET_ISP2B		22
#define TEGRA124_MC_RESET_GPU		23

#define TEGRA124_MC_PTCR		0
#define TEGRA124_MC_DISPLAY0A		1
#define TEGRA124_MC_DISPLAY0AB		2
#define TEGRA124_MC_DISPLAY0B		3
#define TEGRA124_MC_DISPLAY0BB		4
#define TEGRA124_MC_DISPLAY0C		5
#define TEGRA124_MC_DISPLAY0CB		6
#define TEGRA124_MC_AFIR		14
#define TEGRA124_MC_AVPCARM7R		15
#define TEGRA124_MC_DISPLAYHC		16
#define TEGRA124_MC_DISPLAYHCB		17
#define TEGRA124_MC_HDAR		21
#define TEGRA124_MC_HOST1XDMAR		22
#define TEGRA124_MC_HOST1XR		23
#define TEGRA124_MC_MSENCSRD		28
#define TEGRA124_MC_PPCSAHBDMAR		29
#define TEGRA124_MC_PPCSAHBSLVR		30
#define TEGRA124_MC_SATAR		31
#define TEGRA124_MC_VDEBSEVR		34
#define TEGRA124_MC_VDEMBER		35
#define TEGRA124_MC_VDEMCER		36
#define TEGRA124_MC_VDETPER		37
#define TEGRA124_MC_MPCORELPR		38
#define TEGRA124_MC_MPCORER		39
#define TEGRA124_MC_MSENCSWR		43
#define TEGRA124_MC_AFIW		49
#define TEGRA124_MC_AVPCARM7W		50
#define TEGRA124_MC_HDAW		53
#define TEGRA124_MC_HOST1XW		54
#define TEGRA124_MC_MPCORELPW		56
#define TEGRA124_MC_MPCOREW		57
#define TEGRA124_MC_PPCSAHBDMAW		59
#define TEGRA124_MC_PPCSAHBSLVW		60
#define TEGRA124_MC_SATAW		61
#define TEGRA124_MC_VDEBSEVW		62
#define TEGRA124_MC_VDEDBGW		63
#define TEGRA124_MC_VDEMBEW		64
#define TEGRA124_MC_VDETPMW		65
#define TEGRA124_MC_ISPRA		68
#define TEGRA124_MC_ISPWA		70
#define TEGRA124_MC_ISPWB		71
#define TEGRA124_MC_XUSB_HOSTR		74
#define TEGRA124_MC_XUSB_HOSTW		75
#define TEGRA124_MC_XUSB_DEVR		76
#define TEGRA124_MC_XUSB_DEVW		77
#define TEGRA124_MC_ISPRAB		78
#define TEGRA124_MC_ISPWAB		80
#define TEGRA124_MC_ISPWBB		81
#define TEGRA124_MC_TSECSRD		84
#define TEGRA124_MC_TSECSWR		85
#define TEGRA124_MC_A9AVPSCR		86
#define TEGRA124_MC_A9AVPSCW		87
#define TEGRA124_MC_GPUSRD		88
#define TEGRA124_MC_GPUSWR		89
#define TEGRA124_MC_DISPLAYT		90
#define TEGRA124_MC_SDMMCRA		96
#define TEGRA124_MC_SDMMCRAA		97
#define TEGRA124_MC_SDMMCR		98
#define TEGRA124_MC_SDMMCRAB		99
#define TEGRA124_MC_SDMMCWA		100
#define TEGRA124_MC_SDMMCWAA		101
#define TEGRA124_MC_SDMMCW		102
#define TEGRA124_MC_SDMMCWAB		103
#define TEGRA124_MC_VICSRD		108
#define TEGRA124_MC_VICSWR		109
#define TEGRA124_MC_VIW			114
#define TEGRA124_MC_DISPLAYD		115

#endif
