/* SPDX-License-Identifier: GPL-2.0 */
#ifndef DT_BINDINGS_MEMORY_TEGRA30_MC_H
#define DT_BINDINGS_MEMORY_TEGRA30_MC_H

#define TEGRA_SWGROUP_PTC	0
#define TEGRA_SWGROUP_DC	1
#define TEGRA_SWGROUP_DCB	2
#define TEGRA_SWGROUP_EPP	3
#define TEGRA_SWGROUP_G2	4
#define TEGRA_SWGROUP_MPE	5
#define TEGRA_SWGROUP_VI	6
#define TEGRA_SWGROUP_AFI	7
#define TEGRA_SWGROUP_AVPC	8
#define TEGRA_SWGROUP_NV	9
#define TEGRA_SWGROUP_NV2	10
#define TEGRA_SWGROUP_HDA	11
#define TEGRA_SWGROUP_HC	12
#define TEGRA_SWGROUP_PPCS	13
#define TEGRA_SWGROUP_SATA	14
#define TEGRA_SWGROUP_VDE	15
#define TEGRA_SWGROUP_MPCORELP	16
#define TEGRA_SWGROUP_MPCORE	17
#define TEGRA_SWGROUP_ISP	18

#define TEGRA30_MC_RESET_AFI		0
#define TEGRA30_MC_RESET_AVPC		1
#define TEGRA30_MC_RESET_DC		2
#define TEGRA30_MC_RESET_DCB		3
#define TEGRA30_MC_RESET_EPP		4
#define TEGRA30_MC_RESET_2D		5
#define TEGRA30_MC_RESET_HC		6
#define TEGRA30_MC_RESET_HDA		7
#define TEGRA30_MC_RESET_ISP		8
#define TEGRA30_MC_RESET_MPCORE		9
#define TEGRA30_MC_RESET_MPCORELP	10
#define TEGRA30_MC_RESET_MPE		11
#define TEGRA30_MC_RESET_3D		12
#define TEGRA30_MC_RESET_3D2		13
#define TEGRA30_MC_RESET_PPCS		14
#define TEGRA30_MC_RESET_SATA		15
#define TEGRA30_MC_RESET_VDE		16
#define TEGRA30_MC_RESET_VI		17

#define TEGRA30_MC_PTCR			0
#define TEGRA30_MC_DISPLAY0A		1
#define TEGRA30_MC_DISPLAY0AB		2
#define TEGRA30_MC_DISPLAY0B		3
#define TEGRA30_MC_DISPLAY0BB		4
#define TEGRA30_MC_DISPLAY0C		5
#define TEGRA30_MC_DISPLAY0CB		6
#define TEGRA30_MC_DISPLAY1B		7
#define TEGRA30_MC_DISPLAY1BB		8
#define TEGRA30_MC_EPPUP		9
#define TEGRA30_MC_G2PR			10
#define TEGRA30_MC_G2SR			11
#define TEGRA30_MC_MPEUNIFBR		12
#define TEGRA30_MC_VIRUV		13
#define TEGRA30_MC_AFIR			14
#define TEGRA30_MC_AVPCARM7R		15
#define TEGRA30_MC_DISPLAYHC		16
#define TEGRA30_MC_DISPLAYHCB		17
#define TEGRA30_MC_FDCDRD		18
#define TEGRA30_MC_FDCDRD2		19
#define TEGRA30_MC_G2DR			20
#define TEGRA30_MC_HDAR			21
#define TEGRA30_MC_HOST1XDMAR		22
#define TEGRA30_MC_HOST1XR		23
#define TEGRA30_MC_IDXSRD		24
#define TEGRA30_MC_IDXSRD2		25
#define TEGRA30_MC_MPE_IPRED		26
#define TEGRA30_MC_MPEAMEMRD		27
#define TEGRA30_MC_MPECSRD		28
#define TEGRA30_MC_PPCSAHBDMAR		29
#define TEGRA30_MC_PPCSAHBSLVR		30
#define TEGRA30_MC_SATAR		31
#define TEGRA30_MC_TEXSRD		32
#define TEGRA30_MC_TEXSRD2		33
#define TEGRA30_MC_VDEBSEVR		34
#define TEGRA30_MC_VDEMBER		35
#define TEGRA30_MC_VDEMCER		36
#define TEGRA30_MC_VDETPER		37
#define TEGRA30_MC_MPCORELPR		38
#define TEGRA30_MC_MPCORER		39
#define TEGRA30_MC_EPPU			40
#define TEGRA30_MC_EPPV			41
#define TEGRA30_MC_EPPY			42
#define TEGRA30_MC_MPEUNIFBW		43
#define TEGRA30_MC_VIWSB		44
#define TEGRA30_MC_VIWU			45
#define TEGRA30_MC_VIWV			46
#define TEGRA30_MC_VIWY			47
#define TEGRA30_MC_G2DW			48
#define TEGRA30_MC_AFIW			49
#define TEGRA30_MC_AVPCARM7W		50
#define TEGRA30_MC_FDCDWR		51
#define TEGRA30_MC_FDCDWR2		52
#define TEGRA30_MC_HDAW			53
#define TEGRA30_MC_HOST1XW		54
#define TEGRA30_MC_ISPW			55
#define TEGRA30_MC_MPCORELPW		56
#define TEGRA30_MC_MPCOREW		57
#define TEGRA30_MC_MPECSWR		58
#define TEGRA30_MC_PPCSAHBDMAW		59
#define TEGRA30_MC_PPCSAHBSLVW		60
#define TEGRA30_MC_SATAW		61
#define TEGRA30_MC_VDEBSEVW		62
#define TEGRA30_MC_VDEDBGW		63
#define TEGRA30_MC_VDEMBEW		64
#define TEGRA30_MC_VDETPMW		65

#endif
