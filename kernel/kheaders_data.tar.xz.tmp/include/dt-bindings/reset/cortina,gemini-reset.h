/* SPDX-License-Identifier: GPL-2.0 */
#ifndef _DT_BINDINGS_RESET_CORTINA_GEMINI_H
#define _DT_BINDINGS_RESET_CORTINA_GEMINI_H

#define GEMINI_RESET_DRAM	0
#define GEMINI_RESET_FLASH	1
#define GEMINI_RESET_IDE	2
#define GEMINI_RESET_RAID	3
#define GEMINI_RESET_SECURITY	4
#define GEMINI_RESET_GMAC0	5
#define GEMINI_RESET_GMAC1	6
#define GEMINI_RESET_PCI	7
#define GEMINI_RESET_USB0	8
#define GEMINI_RESET_USB1	9
#define GEMINI_RESET_DMAC	10
#define GEMINI_RESET_APB	11
#define GEMINI_RESET_LPC	12
#define GEMINI_RESET_LCD	13
#define GEMINI_RESET_INTCON0	14
#define GEMINI_RESET_INTCON1	15
#define GEMINI_RESET_RTC	16
#define GEMINI_RESET_TIMER	17
#define GEMINI_RESET_UART	18
#define GEMINI_RESET_SSP	19
#define GEMINI_RESET_GPIO0	20
#define GEMINI_RESET_GPIO1	21
#define GEMINI_RESET_GPIO2	22
#define GEMINI_RESET_WDOG	23
#define GEMINI_RESET_EXTERN	24
#define GEMINI_RESET_CIR	25
#define GEMINI_RESET_SATA0	26
#define GEMINI_RESET_SATA1	27
#define GEMINI_RESET_TVC	28
#define GEMINI_RESET_CPU1	30
#define GEMINI_RESET_GLOBAL	31

#endif
