/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */




#ifndef _UAPI_KBASE_MODEL_DUMMY_H_
#define _UAPI_KBASE_MODEL_DUMMY_H_

#include <linux/types.h>

#define KBASE_DUMMY_MODEL_COUNTER_HEADER_DWORDS (4)
#if MALI_USE_CSF
#define KBASE_DUMMY_MODEL_COUNTER_PER_CORE (65)
#else 
#define KBASE_DUMMY_MODEL_COUNTER_PER_CORE (60)
#endif 
#define KBASE_DUMMY_MODEL_COUNTERS_PER_BIT (4)
#define KBASE_DUMMY_MODEL_COUNTER_ENABLED(enable_mask, ctr_idx) \
	(enable_mask & (1 << (ctr_idx / KBASE_DUMMY_MODEL_COUNTERS_PER_BIT)))

#define KBASE_DUMMY_MODEL_HEADERS_PER_BLOCK 4
#define KBASE_DUMMY_MODEL_COUNTERS_PER_BLOCK KBASE_DUMMY_MODEL_COUNTER_PER_CORE
#define KBASE_DUMMY_MODEL_VALUES_PER_BLOCK \
	(KBASE_DUMMY_MODEL_COUNTERS_PER_BLOCK + KBASE_DUMMY_MODEL_HEADERS_PER_BLOCK)
#define KBASE_DUMMY_MODEL_BLOCK_SIZE (KBASE_DUMMY_MODEL_VALUES_PER_BLOCK * sizeof(__u32))
#define KBASE_DUMMY_MODEL_MAX_MEMSYS_BLOCKS 8
#define KBASE_DUMMY_MODEL_MAX_SHADER_CORES 32
#define KBASE_DUMMY_MODEL_MAX_FIRMWARE_BLOCKS 0
#define KBASE_DUMMY_MODEL_MAX_NUM_HARDWARE_BLOCKS \
	(1 + 1 + KBASE_DUMMY_MODEL_MAX_MEMSYS_BLOCKS + KBASE_DUMMY_MODEL_MAX_SHADER_CORES)
#define KBASE_DUMMY_MODEL_MAX_NUM_PERF_BLOCKS \
	(KBASE_DUMMY_MODEL_MAX_NUM_HARDWARE_BLOCKS + KBASE_DUMMY_MODEL_MAX_FIRMWARE_BLOCKS)
#define KBASE_DUMMY_MODEL_COUNTER_TOTAL \
	(KBASE_DUMMY_MODEL_MAX_NUM_PERF_BLOCKS * KBASE_DUMMY_MODEL_COUNTER_PER_CORE)
#define KBASE_DUMMY_MODEL_MAX_VALUES_PER_SAMPLE \
	(KBASE_DUMMY_MODEL_MAX_NUM_PERF_BLOCKS * KBASE_DUMMY_MODEL_VALUES_PER_BLOCK)
#define KBASE_DUMMY_MODEL_MAX_SAMPLE_SIZE \
	(KBASE_DUMMY_MODEL_MAX_NUM_PERF_BLOCKS * KBASE_DUMMY_MODEL_BLOCK_SIZE)


#define DUMMY_IMPLEMENTATION_SHADER_PRESENT (0xFull)
#define DUMMY_IMPLEMENTATION_SHADER_PRESENT_TBEX (0x7FFFull)
#define DUMMY_IMPLEMENTATION_SHADER_PRESENT_TODX (0x3FFull)
#define DUMMY_IMPLEMENTATION_SHADER_PRESENT_TTUX (0x7FFull)
#define DUMMY_IMPLEMENTATION_SHADER_PRESENT_TTIX (0xFFFull)
#define DUMMY_IMPLEMENTATION_SHADER_PRESENT_TKRX (0x1FFFull)
#define DUMMY_IMPLEMENTATION_L2_PRESENT (0x1ull)
#define DUMMY_IMPLEMENTATION_TILER_PRESENT (0x1ull)
#define DUMMY_IMPLEMENTATION_STACK_PRESENT (0xFull)


#endif 
