/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */




#ifndef _UAPI_KBASE_KINSTR_JM_READER_H_
#define _UAPI_KBASE_KINSTR_JM_READER_H_


enum kbase_kinstr_jm_reader_atom_state {
	KBASE_KINSTR_JM_READER_ATOM_STATE_QUEUE,
	KBASE_KINSTR_JM_READER_ATOM_STATE_START,
	KBASE_KINSTR_JM_READER_ATOM_STATE_STOP,
	KBASE_KINSTR_JM_READER_ATOM_STATE_COMPLETE,
	KBASE_KINSTR_JM_READER_ATOM_STATE_COUNT
};

#endif 
