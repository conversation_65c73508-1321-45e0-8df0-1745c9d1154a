/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */


#ifndef _UAPI_KBASE_CSF_ERRORS_DUMPFAULT_H_
#define _UAPI_KBASE_CSF_ERRORS_DUMPFAULT_H_


enum dumpfault_error_type {
	DF_NO_ERROR = 0,
	DF_CSG_SUSPEND_TIMEOUT,
	DF_CSG_TERMINATE_TIMEOUT,
	DF_CSG_START_TIMEOUT,
	DF_CSG_RESUME_TIMEOUT,
	DF_CSG_EP_CFG_TIMEOUT,
	DF_CSG_STATUS_UPDATE_TIMEOUT,
	DF_PROGRESS_TIMER_TIMEOUT,
	DF_FW_INTERNAL_ERROR,
	DF_CS_FATAL,
	DF_CS_FAULT,
	DF_FENCE_WAIT_TIMEOUT,
	DF_PROTECTED_MODE_EXIT_TIMEOUT,
	DF_PROTECTED_MODE_ENTRY_FAILURE,
	DF_PING_REQUEST_TIMEOUT,
	DF_CORE_DOWNSCALE_REQUEST_TIMEOUT,
	DF_TILER_OOM,
	DF_GPU_PAGE_FAULT,
	DF_BUS_FAULT,
	DF_GPU_PROTECTED_FAULT,
	DF_AS_ACTIVE_STUCK,
	DF_GPU_SOFT_RESET_FAILURE,
};

#endif 
