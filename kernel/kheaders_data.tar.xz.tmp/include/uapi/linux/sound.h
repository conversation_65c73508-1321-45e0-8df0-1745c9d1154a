/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */
#ifndef _UAPI_LINUX_SOUND_H
#define _UAPI_LINUX_SOUND_H



#include <linux/fs.h>

#define SND_DEV_CTL		0	
#define SND_DEV_SEQ		1	
#define SND_DEV_MIDIN		2	
#define SND_DEV_DSP		3	
#define SND_DEV_AUDIO		4	
#define SND_DEV_DSP16		5	
	
#define SND_DEV_UNUSED		6
#define SND_DEV_AWFM		7	
#define SND_DEV_SEQ2		8	
	

#define SND_DEV_SYNTH		9	
#define SND_DEV_DMFM		10	
#define SND_DEV_UNKNOWN11	11
#define SND_DEV_ADSP		12	
#define SND_DEV_AMIDI		13	
#define SND_DEV_ADMMIDI		14	


#endif 
