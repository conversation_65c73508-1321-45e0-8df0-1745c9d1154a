/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */


#ifndef _UAPI_ASM_GENERIC_INT_L64_H
#define _UAPI_ASM_GENERIC_INT_L64_H

#include <asm/bitsperlong.h>

#ifndef __ASSEMBLY__


typedef __signed__ char __s8;
typedef unsigned char __u8;

typedef __signed__ short __s16;
typedef unsigned short __u16;

typedef __signed__ int __s32;
typedef unsigned int __u32;

typedef __signed__ long __s64;
typedef unsigned long __u64;

#endif 


#endif 
