/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */
#ifndef _UAPI_ASM_GENERIC_RESOURCE_H
#define _UAPI_ASM_GENERIC_RESOURCE_H



#define R<PERSON>IMIT_CPU		0	
#define R<PERSON><PERSON>IT_FSIZE		1	
#define R<PERSON><PERSON>IT_DATA		2	
#define RLIMIT_STACK		3	
#define RLIM<PERSON>_CORE		4	

#ifndef RLIMIT_RSS
# define RLIMIT_RSS		5	
#endif

#ifndef RLIMIT_NPROC
# define RLIMIT_NPROC		6	
#endif

#ifndef RLIMIT_NOFILE
# define RLIMIT_NOFILE		7	
#endif

#ifndef RLIMIT_MEMLOCK
# define RLIMIT_MEMLOCK		8	
#endif

#ifndef RLIMIT_AS
# define RLIMIT_AS		9	
#endif

#define RLIMIT_LOCKS		10	
#define RLIMIT_SIGPENDING	11	
#define RLIMIT_MSGQUEUE		12	
#define RLIMIT_NICE		13	
#define RLIMIT_RTPRIO		14	
#define RLIMIT_RTTIME		15	
#define R<PERSON>IM_NLIMITS		16


#ifndef RLIM_INFINITY
# define R<PERSON>IM_INFINITY		(~0UL)
#endif


#endif 
