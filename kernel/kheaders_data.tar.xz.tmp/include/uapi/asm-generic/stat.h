/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */
#ifndef __ASM_GENERIC_STAT_H
#define __ASM_GENERIC_STAT_H



#include <asm/bitsperlong.h>

#define STAT_HAVE_NSEC 1

struct stat {
	unsigned long	st_dev;		
	unsigned long	st_ino;		
	unsigned int	st_mode;	
	unsigned int	st_nlink;	
	unsigned int	st_uid;		
	unsigned int	st_gid;		
	unsigned long	st_rdev;	
	unsigned long	__pad1;
	long		st_size;	
	int		st_blksize;	
	int		__pad2;
	long		st_blocks;	
	long		st_atime;	
	unsigned long	st_atime_nsec;
	long		st_mtime;	
	unsigned long	st_mtime_nsec;
	long		st_ctime;	
	unsigned long	st_ctime_nsec;
	unsigned int	__unused4;
	unsigned int	__unused5;
};


#if __BITS_PER_LONG != 64 || defined(__ARCH_WANT_STAT64)
struct stat64 {
	unsigned long long st_dev;	
	unsigned long long st_ino;	
	unsigned int	st_mode;	
	unsigned int	st_nlink;	
	unsigned int	st_uid;		
	unsigned int	st_gid;		
	unsigned long long st_rdev;	
	unsigned long long __pad1;
	long long	st_size;	
	int		st_blksize;	
	int		__pad2;
	long long	st_blocks;	
	int		st_atime;	
	unsigned int	st_atime_nsec;
	int		st_mtime;	
	unsigned int	st_mtime_nsec;
	int		st_ctime;	
	unsigned int	st_ctime_nsec;
	unsigned int	__unused4;
	unsigned int	__unused5;
};
#endif

#endif 
