/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */


#ifndef _DW_HDCP_HOST_LIB_DRIVER_LINUX_IF_H_
#define _DW_HDCP_HOST_LIB_DRIVER_LINUX_IF_H_

#include <linux/ioctl.h>
#include <linux/types.h>

#define HL_DRIVER_ALLOCATE_DYNAMIC_MEM 0xffffffff

enum {
	HL_DRV_NR_MIN = 0x10,
	HL_DRV_NR_INIT,
	HL_DRV_NR_MEMINFO,
	HL_DRV_NR_LOAD_CODE,
	HL_DRV_NR_READ_DATA,
	HL_DRV_NR_WRITE_DATA,
	HL_DRV_NR_MEMSET_DATA,
	HL_DRV_NR_READ_HPI,
	HL_DRV_NR_WRITE_HPI,

	RK_DRV_NR_GET_STATUS,
	RK_DRV_NR_RESET,

	HL_DRV_NR_MAX
};


#define HL_DRV_IOC_INIT    _IOW('H', HL_DRV_NR_INIT, struct hl_drv_ioc_meminfo)


#define HL_DRV_IOC_MEMINFO _IOR('H', HL_DRV_NR_MEMINFO, struct hl_drv_ioc_meminfo)

struct hl_drv_ioc_meminfo {
	__u32 hpi_base;
	__u32 code_base;
	__u32 code_size;
	__u32 data_base;
	__u32 data_size;
};


#define HL_DRV_IOC_LOAD_CODE _IOW('H', HL_DRV_NR_LOAD_CODE, struct hl_drv_ioc_code)

struct hl_drv_ioc_code {
	__u32 len;
	__u8 data[];
};


#define HL_DRV_IOC_READ_DATA  _IOWR('H', HL_DRV_NR_READ_DATA, struct hl_drv_ioc_data)
#define HL_DRV_IOC_WRITE_DATA  _IOW('H', HL_DRV_NR_WRITE_DATA, struct hl_drv_ioc_data)


#define HL_DRV_IOC_MEMSET_DATA _IOW('H', HL_DRV_NR_MEMSET_DATA, struct hl_drv_ioc_data)

struct hl_drv_ioc_data {
	__u32 offset;
	__u32 len;
	__u8 data[];
};


#define HL_DRV_IOC_READ_HPI _IOWR('H', HL_DRV_NR_READ_HPI, struct hl_drv_ioc_hpi_reg)
#define HL_DRV_IOC_WRITE_HPI _IOW('H', HL_DRV_NR_WRITE_HPI, struct hl_drv_ioc_hpi_reg)

struct hl_drv_ioc_hpi_reg {
	__u32 offset;
	__u32 value;
};

#define RK_DRV_IOC_GET_STATUS _IOR('H', RK_DRV_NR_GET_STATUS, struct hl_drv_ioc_status)

struct hl_drv_ioc_status {
	__u32 connected_status;
	__u32 booted_status;
};

#define RK_DRV_IOC_RESET _IOR('H', RK_DRV_NR_RESET, __u32)

#endif 
