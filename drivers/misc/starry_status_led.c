/*
 * linux/drivers/misc/starry_status_led.c
 * Copyright (C) 2021, CZUR TECH CO.,LTD.
 *
 *
 * simple PWM based backlight control, board code has to setup
 * 1) pin configuration so PWM waveforms can output
 * 2) platform_data being correctly configured
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

#include <linux/gpio/consumer.h>
#include <linux/gpio.h>
#include <linux/of_gpio.h>
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
//#include <linux/adc.h>
#include <linux/platform_device.h>
#include <linux/fb.h>
#include <linux/backlight.h>
#include <linux/err.h>
#include <linux/pwm.h>
#include <linux/pwm_backlight.h>
#include <linux/regulator/consumer.h>
#include <linux/slab.h>

#include <linux/iio/iio.h>
#include <linux/iio/machine.h>
#include <linux/iio/driver.h>
#include <linux/iio/consumer.h>

#include <linux/sched.h>

#include <linux/interrupt.h>
#include <linux/irq.h>

#include <linux/wakelock.h>

#include "../input/touchscreen/tp_suspend.h"

#define INVALID_ADVALUE			-1

#define BOOTCOMPLETE_COUNT 1700

int g_bt_charger_flag;
static int pwm_led_flag  = 0;

static struct wake_lock wakelock;

struct gpio_dev {
	unsigned int num;
	int flag;
};

struct status_led_ctrl_data {
    struct platform_device	*pdev;
	struct pwm_device	*pwms;
	struct pwm_state state;
	int pwm_led_active_level;
	u64 period;
	u64 duty;
	unsigned int count_time;
	//unsigned int gpio_red_led;
	struct gpio_dev gpio_red_led;
	int pwm_gpio;
	int light_sleep_flag;
	int red_led_flag;
	struct device		*dev;
	struct delayed_work led_delay_work;
	struct  tp_device  tp;
};

extern int get_Starry_bt_charger_det_flag(void);

static ssize_t
store_pwm_endable(struct device *dev, struct device_attribute *attr, const char *buf, size_t count)
{
    struct status_led_ctrl_data *ddata = dev_get_drvdata(dev);
    int err, w;

    err = kstrtoint(buf, 10, &w);
    if (err)
        return err;
    if (w!=0){
        ddata->state.enabled = true;
    }else{
        ddata->state.enabled = false;
    }
	pwm_apply_state(ddata->pwms, &ddata->state);
	return count;
}

static DEVICE_ATTR(status_pwm_endable, S_IRUGO | S_IWUSR, NULL, store_pwm_endable);


static ssize_t
store_pwm_set_duty(struct device *dev, struct device_attribute *attr, const char *buf, size_t count)
{
    struct status_led_ctrl_data *ddata = dev_get_drvdata(dev);
    int err, w;

    err = kstrtoint(buf, 10, &w);
    if (err){
        return err;
    }

    //ddata->state.period = ddata->period;
		if(0 == ddata->pwm_led_active_level)
			w = 50000 - w;
		
    ddata->state.duty_cycle = w;
    pwm_apply_state(ddata->pwms, &ddata->state);

	return count;
}

static DEVICE_ATTR(status_pwm_set_duty, S_IRUGO | S_IWUSR, NULL, store_pwm_set_duty);

static ssize_t
store_redled_onoff(struct device *dev, struct device_attribute *attr, const char *buf, size_t count)
{
	struct status_led_ctrl_data *ddata = dev_get_drvdata(dev);
    int err, w;

    err = kstrtoint(buf, 10, &w);
    if (err)
        return err;
    gpio_set_value(ddata->gpio_red_led.num, w == 0? ddata->gpio_red_led.flag : !ddata->gpio_red_led.flag);
	return count;
}

static DEVICE_ATTR(status_redled_onoff, S_IRUGO | S_IWUSR, NULL, store_redled_onoff);


static struct attribute *fan_ctrl_attributes[] = {
	&dev_attr_status_pwm_endable.attr,
	&dev_attr_status_pwm_set_duty.attr,
	&dev_attr_status_redled_onoff.attr,
	NULL
};

static struct attribute_group fan_ctrl_attribute_group = {
	.attrs	= fan_ctrl_attributes,
};

static int sleep_status = 0;

void led_sleep_in(void)
{
	sleep_status = 1;
}
EXPORT_SYMBOL(led_sleep_in);

void led_sleep_out(void)
{
	sleep_status = 0;
}
EXPORT_SYMBOL(led_sleep_out);

void starry_set_bt_charger_flag(int ischarger)
{
    g_bt_charger_flag = ischarger;
}
EXPORT_SYMBOL(starry_set_bt_charger_flag);

static int status_led_ctrl_parse_dt(struct device *dev,
				  struct status_led_ctrl_data *data)
{
	int ret;
	int flags;

	memset(data, 0, sizeof(*data));

	data->pwms = devm_pwm_get(dev, NULL);
	if (IS_ERR(data->pwms)){
        goto pwmIsFail;
    }
	pwm_get_state(data->pwms, &data->state);
	pwm_init_state(data->pwms, &data->state);
	dev_info(dev, "get pwm state: period = %lld, duty_cycle = %lld, polarity = %d, enabled = %d\n",
		data->state.period, data->state.duty_cycle, data->state.polarity, data->state.enabled);

	data->period = data->state.period;
	data->duty = data->state.period;

	data->state.duty_cycle = data->state.period;
	data->state.enabled = true;
	pwm_apply_state(data->pwms, &data->state);

	dev_info(dev, "get pwm state: period = %lld, duty_cycle = %lld, polarity = %d, enabled = %d\n",
		data->state.period, data->state.duty_cycle, data->state.polarity, data->state.enabled);

	if(of_property_read_u32(dev->of_node, "pwm-led-active-level", &(data->pwm_led_active_level))) {
		dev_err(dev, "no pwm-led-active-level defined, set 0 as the default value\n");
		data->pwm_led_active_level = 0;
	}

    data->pwm_gpio = of_get_named_gpio_flags(data->pwms->chip->dev->of_node, "gpio", 0, (enum of_gpio_flags *)&flags);
	data->gpio_red_led.num = of_get_named_gpio_flags(dev->of_node, "red_led_en", 0, (enum of_gpio_flags *)&data->gpio_red_led.flag);
	dev_info(dev, "red led gpio num = %d, flag = %d\n", data->gpio_red_led.num, data->gpio_red_led.flag);
	if (!gpio_is_valid(data->gpio_red_led.num)){
	    goto red_led_gpio_fail;
	}
	ret = devm_gpio_request_one(dev, data->gpio_red_led.num, GPIOF_DIR_OUT|data->gpio_red_led.flag? GPIOF_INIT_HIGH : GPIOF_INIT_LOW, "red_led_en");
	return 0;
red_led_gpio_fail:
    pwm_free(data->pwms);
pwmIsFail:
    return -ENODEV;;
}

static void led_handler(struct work_struct *ws)
{
	struct status_led_ctrl_data *ddata =
		container_of(ws, struct status_led_ctrl_data, led_delay_work.work);

		if(1 == sleep_status)
		{
			if(ddata->count_time >= BOOTCOMPLETE_COUNT)
			{
				ddata->count_time = 1600;
				ddata->duty = 50000;
			}	
		}	

    if(ddata->count_time < BOOTCOMPLETE_COUNT){
        ddata->duty -= 1000;
        if (ddata->duty <= 0 )
            ddata->duty = ddata->period;
    	ddata->count_time++;
    //	printk("@@@@####count:%d\n", ddata->count_time);
        gpio_set_value(ddata->gpio_red_led.num, ddata->gpio_red_led.flag);
        //ddata->state.period = ddata->period;
        ddata->state.duty_cycle = ddata->duty;
        pwm_apply_state(ddata->pwms, &ddata->state);

    }else if (ddata->count_time == BOOTCOMPLETE_COUNT){
		if(ddata->pwm_led_active_level)
			ddata->duty = ddata->state.polarity? 0 : ddata->state.period;
		else
			ddata->duty = ddata->state.polarity? ddata->state.period : 0;

        ddata->count_time++;

        ddata->state.duty_cycle = ddata->duty;
		//dev_info("%s(%d), ddata->state.duty_cycle = %lld\n", __func__, __LINE__, ddata->state.duty_cycle);
        pwm_apply_state(ddata->pwms, &ddata->state);

 //       g_bt_charger_flag = get_Starry_bt_charger_det_flag();
    }else{
        if(pwm_led_flag == 0){
            ddata->duty += 2000;
            if (ddata->duty >= ddata->period){
                ddata->duty = ddata->period;
                pwm_led_flag = 1;
            }
        }else{
            ddata->duty -= 2000;
            if (ddata->duty <= 0){
                ddata->duty = 0;
                pwm_led_flag = 0;
            }
        }
//        ddata->red_led_flag ^= 1;
        if (ddata->light_sleep_flag == 1){
            ddata->duty = ddata->state.polarity? 0 : ddata->state.period;
            ddata->state.duty_cycle = ddata->duty;
            pwm_apply_state(ddata->pwms, &ddata->state);

//            if (g_bt_charger_flag)
//                gpio_set_value(ddata->gpio_red_led.num, ddata->red_led_flag);
//            else
//                gpio_set_value(ddata->gpio_red_led.num, 0);
        }
    }
	if(1 == sleep_status)
		schedule_delayed_work(&ddata->led_delay_work, msecs_to_jiffies(100));
	else
		schedule_delayed_work(&ddata->led_delay_work, ddata->count_time < BOOTCOMPLETE_COUNT ? msecs_to_jiffies(10): msecs_to_jiffies(100));
}

static int status_led_early_suspend(struct tp_device *tp_d)
{
	struct status_led_ctrl_data *ddata = container_of(tp_d, struct status_led_ctrl_data, tp);

	ddata->light_sleep_flag = 1;
    gpio_set_value(ddata->gpio_red_led.num, ddata->gpio_red_led.flag);
    ddata->duty = 0;
    pwm_led_flag = 0;
    //ddata->state.period = ddata->period;
    ddata->state.duty_cycle = ddata->duty;
    pwm_apply_state(ddata->pwms, &ddata->state);

	return 0;
}

static int status_led_late_resume(struct tp_device *tp_d)
{
	struct status_led_ctrl_data *ddata = container_of(tp_d, struct status_led_ctrl_data, tp);

	ddata->light_sleep_flag = 0;
//	pwm_config(ddata->pwms, 0, ddata->period);

    //ddata->state.period = ddata->period;
    ddata->state.duty_cycle = ddata->duty;

    pwm_apply_state(ddata->pwms, &ddata->state);

//	pwm_enable(ddata->pwms);
    gpio_set_value(ddata->gpio_red_led.num, ddata->gpio_red_led.flag);
	return 0;
}



static int status_led_ctrl_probe(struct platform_device *pdev)
{    
    struct device *dev = &pdev->dev;
    struct status_led_ctrl_data *ddata = NULL;

    int ret, err;
    //wake_lock(&wakelock);
    ddata = devm_kzalloc(dev, sizeof(struct status_led_ctrl_data), GFP_KERNEL);

    if (!ddata) {
        return -ENOMEM;
    }

	ret = status_led_ctrl_parse_dt(&pdev->dev, ddata);
	if (ret < 0) {
        dev_err(&pdev->dev, "failed to find platform data\n");
        goto err_alloc;
	}

	ddata->pdev = pdev;

	platform_set_drvdata(pdev, ddata);
    dev_set_drvdata(&pdev->dev, ddata);

    ddata->tp.tp_resume = status_led_late_resume;
    ddata->tp.tp_suspend = status_led_early_suspend;
    tp_register_fb(&ddata->tp);
    ddata->light_sleep_flag = 0;
    ddata->count_time = 0;

    INIT_DELAYED_WORK(&ddata->led_delay_work, led_handler);
    schedule_delayed_work(&ddata->led_delay_work, msecs_to_jiffies(10));

    err = sysfs_create_group(&pdev->dev.kobj, &fan_ctrl_attribute_group);
    if (err){
        ret = -1;
        goto err_alloc;
    }
	return 0;

err_alloc:
    devm_kfree(dev, ddata);
	return ret;
}

static int status_led_ctrl_remove(struct platform_device *pdev)
{
    struct device *dev = &pdev->dev;
    struct status_led_ctrl_data *ddata = dev_get_drvdata(dev);

    sysfs_remove_group(&pdev->dev.kobj, &fan_ctrl_attribute_group);
    cancel_delayed_work_sync(&ddata->led_delay_work);
    tp_unregister_fb(&ddata->tp);

    if (!IS_ERR(ddata->pwms)){
        pwm_free(ddata->pwms);
    }
    if (gpio_is_valid(ddata->gpio_red_led.num)){
        gpio_free(ddata->gpio_red_led.num);
    }
    devm_kfree(dev, ddata);
 //   printk("%s, %d", __func__, __LINE__);
	return 0;
}

static void status_led_ctrl_shutdown(struct platform_device *pdev)
{
    struct device *dev = &pdev->dev;
    struct status_led_ctrl_data *ddata = dev_get_drvdata(dev);
    int ret;

    //ddata->state.period = ddata->period;
    ddata->state.duty_cycle = ddata->duty;
    pwm_apply_state(ddata->pwms, &ddata->state);

    if (!IS_ERR(ddata->pwms)){
        pwm_free(ddata->pwms);
    }
    ret = devm_gpio_request_one(dev, ddata->pwm_gpio, GPIOF_DIR_OUT|GPIOF_INIT_HIGH, "pwm_gpio");
    gpio_set_value(ddata->pwm_gpio, 1);
    gpio_set_value(ddata->gpio_red_led.num, !ddata->gpio_red_led.flag);
  /*  if (gpio_is_valid(ddata->pwm_gpio)){
        devm_gpio_free(dev, ddata->pwm_gpio);
    }*/
}

#ifdef CONFIG_PM_SLEEP
static int status_led_ctrl_suspend(struct device *dev)
{
	return 0;
}

static int status_led_ctrl_resume(struct device *dev)
{
	return 0;
}
#endif

static const struct dev_pm_ops status_led_ctrl_pm_ops = {
#ifdef CONFIG_PM_SLEEP
	.suspend = status_led_ctrl_suspend,
	.resume = status_led_ctrl_resume,
#endif
};

static struct of_device_id status_led_ctrl_of_match[] = {
	{ .compatible = "status_led_ctrl" },
	{ }
};

MODULE_DEVICE_TABLE(of, status_led_ctrl_of_match);

static struct platform_driver status_led_ctrl_driver = {
	.driver		= {
		.name		= "status_led_ctrl",
		.pm		= &status_led_ctrl_pm_ops,
		.of_match_table	= of_match_ptr(status_led_ctrl_of_match),
	},
	.probe		= status_led_ctrl_probe,
	.remove		= status_led_ctrl_remove,
	.shutdown = status_led_ctrl_shutdown,
};

static int __init status_led_ctrl_driver_init(void)
{
    wake_lock_init(&wakelock, WAKE_LOCK_SUSPEND, "starry_led");
	return platform_driver_register(&status_led_ctrl_driver);
}

static void __exit status_led_ctrl_driver_exit(void)
{
	platform_driver_unregister(&status_led_ctrl_driver);
}

//arch_initcall_sync(status_led_ctrl_driver_init);
device_initcall(status_led_ctrl_driver_init);
module_exit(status_led_ctrl_driver_exit);

MODULE_AUTHOR("ZouHerong<<EMAIL>>");
MODULE_DESCRIPTION("Machine status contral Driver");
MODULE_LICENSE("GPL");
MODULE_ALIAS("platform:Machine statuscontral");

