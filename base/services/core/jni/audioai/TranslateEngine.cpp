/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#define LOG_TAG "AudioAI.TranslateEngine.cpp"
#define LOG_NDEBUG 0

#include "TranslateEngine.h"
#include <utils/Log.h>
#include <cutils/properties.h>
#include <cstring>
#include <cstdio>
#include <ctime>
#include <execinfo.h>
#include <stdlib.h>
#include <unistd.h>
#include <typeinfo>
#include <dlfcn.h>
#include <chrono>
#include <thread>
#include "MicAmplitude.h"

// using namespace czsl_asr;

namespace android::server::audioai{

OnAsrResultCallback onAsrResultCallback;             // 成员变量保存ASR结果回调
OnTranslateResultCallback onTranslateResultCallback; // 成员变量保存翻译结果回调
OnErrorCallback onErrorCallback;                     // 成员变量保存错误回调
OnThreadExitCallback onThreadExitCallback;           // 成员变量保存线程退出回调
OnMicAmpCallback onMicAmpCallback;

bool gShouldStart = false;

// #define ASR_HOST "asr-trans-dev.czur.com"
#define ASR_HOST "asr-trans-test.czur.com"
#define SERVER_PORT "443"
#define CHUNK_SIZE 80
#define DUMP_IN_PCM "/mnt/asr_in.pcm"

TranslateEngine::TranslateEngine() {
    ALOGD("TranslateEngine construct");

    // 默认参数
    mAsrLanguage = "CN==EN";
    mOrigLanguage = "CN";
    mTargetLanguage = "EN";
}

TranslateEngine::~TranslateEngine() {
    ALOGD("~TranslateEngine");
}

std::string TranslateEngine::getAsrHost() {
    return mIsPrd ? "asr-trans.czur.com" : "asr-trans-test.czur.com";
}

int TranslateEngine::init(std::string &userId, bool isPrd) {
    ALOGD("TranslateEngine init");

    mIsPrd = isPrd;
    mShouldDeInit = true;

    mDebugFile = std::fopen(DUMP_IN_PCM, "wb");
    if (!mDebugFile) {
        ALOGE("Failed to open debug file");
    }
    
    if (asrInterface_ptr == nullptr) {
        asrInterface_ptr = std::make_shared<asr_interface_t>();
        // 打开共享库
        handle = dlopen("libczslasr.so", RTLD_NOW); // 确保路径正确
        if (!handle) {
            ALOGE("TranslateEngine init dlopen err: %s",dlerror());
            return -1;
        }
        // 清除之前的错误
        dlerror();

        asrInterface_ptr->createF = (AsrInterFace_Create_Func)dlsym(handle, "AsrInterFace_Create");
        if ((error = dlerror()) != NULL) {
            ALOGE("TranslateEngine init createF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->deleteF = (AsrInterFace_Delete_Func)dlsym(handle, "AsrInterFace_Delete");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init deleteF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->initF = (AsrInterFace_Init_Func)dlsym(handle, "AsrInterFace_Init");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init initF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->bindF = (AsrInterFace_BindCallback_Func)dlsym(handle, "AsrInterFace_BindCallback");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init bindF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->hputF = (AsrInterFace_HttpPut_Func)dlsym(handle, "AsrInterFace_HttpPut");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init hputF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->connectF = (AsrInterFace_Connect_Func)dlsym(handle, "AsrInterFace_Connect");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init connectF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->disconnectF = (AsrInterFace_DisConnectServer_Func)dlsym(handle, "AsrInterFace_DisConnectServer");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init codisconnectFnnectF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->pushF = (AsrInterFace_PushBuf_Func)dlsym(handle, "AsrInterFace_PushBuf");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init pushF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->queryF = (AsrInterFace_QueryLang_Func)dlsym(handle, "AsrInterFace_QueryLang");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init queryF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->setpF = (AsrInterFace_SetParam_Func)dlsym(handle, "AsrInterFace_SetParam");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init setpF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->setsF = (AsrInterFace_SetSummary_Func)dlsym(handle, "AsrInterFace_SetSummary");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init setsF err: %s",error);
            dlclose(handle);
            return -1;
        }

        asrInterface_ptr->startF = (AsrInterFace_StartListen_Func)dlsym(handle, "AsrInterFace_StartListen");
        if ((error = dlerror()) != NULL)  {
            ALOGE("TranslateEngine init startF err: %s",error);
            dlclose(handle);
            return -1;
        }
    }

    mUserId = userId;

    // 启动线程
    if (mKeepAsrRunThread == nullptr) {
        mKeepAsrRunThread = new KeepAsrRunThread(this);
    }
    mKeepAsrRunThread->run("KeepAsrRunThread", PRIORITY_NORMAL);

    return 0;
}

int TranslateEngine::startAsr() {
    ALOGD("TranslateEngine startAsr");
    mShouldDeInit = false;
    if (mStartAsrRunThread == nullptr) {
        mStartAsrRunThread = new StartAsrRunThread(this);
    }
    mStartAsrRunThread->run("StartAsrRunThread", PRIORITY_NORMAL);
    return 0;
}

int TranslateEngine::deInit() {
    ALOGD("TranslateEngine deInit");
    if (asrInterface_ptr) {
        int ret = asrInterface_ptr->disconnectF(kws_handle);
        if (ret!= 0) {
            ALOGE("TranslateEngine disconnectF failed");
            return -1;
        }
        else
        {
            ALOGE("TranslateEngine disconnectF success");
        }
        while (!mShouldDeInit) {
            ALOGD("TranslateEngine mShouldDeInit");
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
        ALOGE("TranslateEngine ShouldDeInit");
        mShouldDeInit = false;   
        asrInterface_ptr->deleteF(kws_handle);
        asrInterface_ptr.reset();
        asrInterface_ptr = nullptr;
        dlclose(handle);
    }

    if (mDebugFile) {
        std::fclose(mDebugFile);
        mDebugFile = nullptr;
    }
    return 0;
}

void TranslateEngine::free_lang_list(char** list, int count)
{
    if (list) {
        for (int i = 0; i < count; ++i) {
            delete[] list[i]; // 释放每个字符串
        }
        delete[] list; // 释放字符串数组本身
    }
}

int TranslateEngine::translate(const char *src, int start, int length) {
    // 写入debug文件
    if (mDebugFile) {
        std::fwrite(src + start, length, 1, mDebugFile);
    }

    std::vector<char> buffer(2560);
    buffer.assign(src + start, src + start + length);
    if (asrInterface_ptr) {
        auto start_set = std::chrono::high_resolution_clock::now();
        asrInterface_ptr->pushF(kws_handle,buffer.data(),buffer.size());
        auto end_set = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double, std::milli> elapsed = end_set - start_set;
        if (elapsed.count() > 3)
        {
            ALOGD("TranslateEngine pushF time: %.3f",elapsed.count());
        }
        calculateMicAmplitude(src + start, length);
    }
    return 0;
}

void TranslateEngine::calculateMicAmplitude(const char *src, int length) {
    if (onMicAmpCallback == nullptr) {
        return;
    }
    double rms = calculatePeakAmplitudeAsr(src, length);

    if (onMicAmpCallback) {
        onMicAmpCallback(std::to_string(rms));
    }
}

void TranslateEngine::setOnMicAmpCallback(OnMicAmpCallback callback) {
    onMicAmpCallback = callback;
}

std::string TranslateEngine::getSessionId() {
    return mSessionId;
}

std::vector<std::string> TranslateEngine::getSpAsrLang() {
    return SpAsr;
}

std::vector<std::string> TranslateEngine::getSpOrigLang(){
    return SpOrig;
}

std::vector<std::string> TranslateEngine::getSpTargetLang(){
    return SpTarget;
}

int TranslateEngine::setAsrLanguage(std::string &asrLanguage) {
    mAsrLanguage = asrLanguage;
    // TODO 重新设置参数
    return 0;
}

int TranslateEngine::setAsrName(std::string &name) {
    mAsrName = name;
    return 0;
}
int TranslateEngine::setTimezone(std::string &timezone) {
    mTimezone = timezone;
    return 0;
}

int TranslateEngine::setTranslateLanguage(std::string &origLang, std::string &dstLang) {
    mOrigLanguage = origLang;
    mTargetLanguage = dstLang;
    return 0;
}

int TranslateEngine::setTranslateEnable(bool enable) {
    mTranslateEnable = enable;
    return 0;
}

int TranslateEngine::setSummaryEnable(bool enable) {
    if (asrInterface_ptr) {
        int ret = asrInterface_ptr->setsF(kws_handle,enable);
        if (ret != 0) {
            return -1;
        } 
    }
    return 0;
}

TranslateEngine::KeepAsrRunThread::KeepAsrRunThread(const sp<TranslateEngine>& engine):mEngine(engine)  {
    ALOGD("KeepAsrRunThread construct");
}
TranslateEngine::KeepAsrRunThread::~KeepAsrRunThread() {
    ALOGD("~KeepAsrRunThread");
}
bool TranslateEngine::KeepAsrRunThread::threadLoop() {
    ALOGD("KeepAsrRunThread threadLoop");
    gShouldStart = false;
    mEngine->mShouldDeInit = false;
    if (mEngine->asrInterface_ptr) {
        // 连接服务器
        std::string sn = mEngine->getDeviceSN();
        ALOGD("KeepAsrRunThread threadLoop 1");

        mEngine->kws_handle = mEngine->asrInterface_ptr->createF();
        int ret = mEngine->asrInterface_ptr->initF(mEngine->kws_handle,"/vendor/etc/czslkws_rc/");

        // 绑定回调函数
        ret = mEngine->asrInterface_ptr->bindF(mEngine->kws_handle,on_asrres,on_transres,on_error);
        ALOGD("KeepAsrRunThread threadLoop 2");
        if (ret != 0) {
            ALOGE("TranslateEngine BindCallbackfun failed");
            mEngine->mShouldDeInit = true;
            return false;
        }

        ret = mEngine->asrInterface_ptr->hputF(mEngine->kws_handle,mEngine->getAsrHost().c_str(),SERVER_PORT,sn.c_str(),mEngine->mUserId.c_str(),false);
        ALOGD("KeepAsrRunThread threadLoop 3");
        if (ret != 0) {
            ALOGE("TranslateEngine http put failed");
            mEngine->mShouldDeInit = true;
            return false;
        }

        gShouldStart = true;
        onErrorCallback(-1001, "asr could start");

        char** asrl = nullptr;
        int asrl_count = 0;
        char** tsl = nullptr;
        int tsl_count = 0;
        char** ttl = nullptr;
        int ttl_count = 0;
        ret = mEngine->asrInterface_ptr->queryF(mEngine->kws_handle,&asrl,&asrl_count,&tsl,&tsl_count,&ttl,&ttl_count);
        if (ret != 0) {
            ALOGE("TranslateEngine queryF failed");
            return false;
        } else {
            // 将 C 风格的字符串数组转换为 std::vector<std::string>
            std::vector<std::string> asrl_vec(asrl, asrl + asrl_count);
            std::vector<std::string> tsl_vec(tsl, tsl + tsl_count);
            std::vector<std::string> ttl_vec(ttl, ttl + ttl_count);
            mEngine->SpAsr = asrl_vec;
            mEngine->SpOrig = tsl_vec;
            mEngine->SpTarget = ttl_vec;
            // 释放内存
            mEngine->free_lang_list(asrl, asrl_count);
            mEngine->free_lang_list(tsl, tsl_count);
            mEngine->free_lang_list(ttl, ttl_count);
        }
    } else {
        ALOGE("TranslateEngine init failed");
        return false;
    }
    return false;
}

TranslateEngine::StartAsrRunThread::StartAsrRunThread(const sp<TranslateEngine>& engine):mEngine(engine)  {
    ALOGD("StartAsrRunThread construct");
}
TranslateEngine::StartAsrRunThread::~StartAsrRunThread() {
    ALOGD("~StartAsrRunThread");
}
bool TranslateEngine::StartAsrRunThread::threadLoop() {
    ALOGD("StartAsrRunThread threadLoop");
    if (!gShouldStart) {
        if (onErrorCallback) {
            ALOGD("StartAsrRunThread threadLoop onErrorCallback");
            onErrorCallback(5001, "asr can't start");
        }
        return false;
    }
    if (mEngine->asrInterface_ptr) {
        std::string sn = mEngine->getDeviceSN();
        char* sessionId = nullptr;
        int ret = mEngine->asrInterface_ptr->connectF(mEngine->kws_handle,mEngine->getAsrHost().c_str(),SERVER_PORT, sn.c_str(), mEngine->mUserId.c_str(), false,&sessionId);
        ALOGD("StartAsrRunThread threadLoop 4");
        if (ret != 0) {
            ALOGE("TranslateEngine init failed");
            mEngine->mShouldDeInit = true;
            return false;
        }
        mEngine->mSessionId = sessionId;
        ALOGD("TranslateEngine init success: %s", sessionId);
        if (sessionId != nullptr) {
            delete[] sessionId;
            sessionId = nullptr;
        }

        //设置是否开启翻译等可控参数 mEngine->mTranslateEnable
        ret = mEngine->asrInterface_ptr->setpF(mEngine->kws_handle,mEngine->mTranslateEnable,true,CHUNK_SIZE,mEngine->mAsrLanguage.c_str(),mEngine->mOrigLanguage.c_str(),mEngine->mTargetLanguage.c_str(), mEngine->mAsrName.c_str(), mEngine->mTimezone.c_str(),false);
        ALOGD("StartAsrRunThread threadLoop 6");
        if (ret != 0) {
            ALOGE("TranslateEngine SetParam failed");
            mEngine->mShouldDeInit = true;
            return false;
        }

        //开始监听收发数据
        ret = mEngine->asrInterface_ptr->startF(mEngine->kws_handle);
        ALOGD("StartAsrRunThread threadLoop 7");
        if (ret != 0) {
            ALOGE("TranslateEngine start_listen failed");
            mEngine->mShouldDeInit = true;
            return false;
        }
        mEngine->mShouldDeInit = true;
    } else {
        ALOGE("TranslateEngine init failed");
        return false;
    }
    return false;
}

void TranslateEngine::setOnAsrResultCallback(OnAsrResultCallback callback) {
    onAsrResultCallback = callback;
}
void TranslateEngine::setOnTranslateResultCallback(OnTranslateResultCallback callback) {
    onTranslateResultCallback = callback;
}
void TranslateEngine::setOnErrorCallback(OnErrorCallback callback) {
    onErrorCallback = callback;
}

std::string TranslateEngine::getDeviceSN() {
    const int maxLength = 128;
    char serialNo[maxLength] = {0};
    property_get("ro.serialno", serialNo, "");
    serialNo[strlen(serialNo)] = '\0';
    std::string sn = serialNo;
    return sn;
}

void TranslateEngine::on_asrres(const char* asrres_, const char* roleres) {
    std::string asrres = asrres_;
    std::string delimiter = "=====";
    size_t pos = asrres.find(delimiter);
    std::string asr_end = asrres.substr(0, pos);
    std::string trans_res = asrres.substr(pos + delimiter.length());
    std::string result;
    if (trans_res == "") {
        result = asr_end;
        ALOGD("TranslateEngine capi on_asrres: %s", result.c_str());
    } else {
        result = asr_end + "\n" + trans_res;
        ALOGD("TranslateEngine capi on_asrres: %s", result.c_str());
    }
    if (onAsrResultCallback) {
        ALOGD("onAsrResultCallback");
        onAsrResultCallback(asrres_, roleres);
    }
}

void TranslateEngine::on_transres(const char* transres_, const char* roleres) {
    std::string transres = transres_;
    std::string delimiter = "=====";
    size_t pos = transres.find(delimiter);
    std::string asr_end = transres.substr(0, pos);
    std::string trans_res = transres.substr(pos + delimiter.length());
    std::string result = asr_end+"\n"+trans_res;
    ALOGD("TranslateEngine capi on_transres: %s", result.c_str());
    if (onTranslateResultCallback) {
        ALOGD("onTranslateResultCallback");
        onTranslateResultCallback(transres_, roleres);
    }
}

void TranslateEngine::on_error(int code,const char* msg) {
    ALOGD("TranslateEngine capi on_error err code: %d  msg: %s",code, msg);
    if (onErrorCallback) {
        ALOGD("onErrorCallback");
        onErrorCallback(code, msg);
    }
}

}
