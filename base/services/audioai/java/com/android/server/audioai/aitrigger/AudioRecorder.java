package com.android.server.audioai.aitrigger;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import java.text.SimpleDateFormat;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemProperties;
import java.util.Date;

import android.util.Slog;

import com.android.internal.os.BackgroundThread;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AudioRecorder {
    private static final String TAG = "AudioAI.AudioRecorder";
    private static final boolean DEBUG = true;
    private final static int AUDIO_INPUT = MediaRecorder.AudioSource.MIC;
    private final static int AUDIO_SAMPLE_RATE = 16000;
    private final static int AUDIO_CHANNEL = AudioFormat.CHANNEL_IN_MONO;
    private final static int AUDIO_ENCODING = AudioFormat.ENCODING_PCM_16BIT;

    private final static int AUDIO_CHUNK_MS = 80;
    private int bufferSizeInBytes = 0;
    private AudioRecord audioRecord;
    private Status status = Status.STATUS_NO_READY;
    private String fileName;
    private List<String> filesName = new ArrayList<>();
    private boolean dumpAudioData;
    public TriggerAction action = TriggerAction.ACTION_OTHER;
    private RecordStreamListener translateListener;
    private RecordStreamListener interactionListener;
    private final RecorderHandler mHandler;
    private final RingBuffer ringBuffer;
    private ExecutorService executorService = Executors.newFixedThreadPool(10);
    public boolean mAsrStarted = false;
    public boolean mKwsStarted = false;
    public int mMicStatus = 0;


    private AudioRecorder() {
        mHandler = new RecorderHandler(BackgroundThread.get().getLooper());
        ringBuffer = new RingBuffer(getAudioChunkMsSize() * 125);
    }
    public static AudioRecorder getInstance() {
        return AudioRecorderHolder.instance;
    }

    private static class AudioRecorderHolder {
        /**
         * 由JVM来保证线程安全
         */
        private static AudioRecorder instance = new AudioRecorder();
    }

    private void trace(String msg) {
        if (DEBUG) {
            Slog.d(TAG, msg);
        }
    }

    /**
     * 创建默认的录音对象
     */
    public void createDefaultAudio() {
        // 获得缓冲区字节大小
        bufferSizeInBytes = AudioRecord.getMinBufferSize(AUDIO_SAMPLE_RATE, AUDIO_CHANNEL, AUDIO_ENCODING);
        if (bufferSizeInBytes < getAudioChunkMsSize()) {
            bufferSizeInBytes = getAudioChunkMsSize();
        }
        trace("===createDefaultAudio===" + bufferSizeInBytes);
        // audioRecord = new AudioRecord(AUDIO_INPUT, AUDIO_SAMPLE_RATE, AUDIO_CHANNEL, AUDIO_ENCODING, bufferSizeInBytes);
        // status = Status.STATUS_READY;
    }

    private int getAudioChunkMsSize() {
        // 根据采样率和采样时间计算出缓冲区大小 1 channel 16bit
        return (int) (AUDIO_SAMPLE_RATE * 16 / 8 * AUDIO_CHUNK_MS / 1000);
    }

    public void startRecord() {
        if (audioRecord != null) {
            Slog.e(TAG, "audioRecord未销毁");
            return;
        }
        Slog.d(TAG, "===AudioRecord===startRecord");
        dumpAudioData = SystemProperties.getBoolean("persist.vendor.czur.ai.dump", false);
        if (dumpAudioData) {
            fileName = getSavePath();
            Slog.d(TAG, "dump save path: " + fileName);
        }
        if (action == TriggerAction.ACTION_SELF) {
            audioRecord = new AudioRecord(AUDIO_INPUT, AUDIO_SAMPLE_RATE, AUDIO_CHANNEL, AUDIO_ENCODING, bufferSizeInBytes);
            recordBySelf();
        } else if (action == TriggerAction.ACTION_OTHER){
            recordByOther();
        }
    }

    public void startAi() {
        Slog.d(TAG, "===AudioRecord===startAi======");
        if (mAsrStarted) {
            Slog.d(TAG, "===AudioRecord===startAi======mAsrStarted return");
            return;
        }
        mAsrStarted = true;
        if (!mKwsStarted) {
            Slog.d(TAG, "===AudioRecord===startAi======!mKwsStarted");
            SystemProperties.set("persist.vendor.czur.ai.interaction.trigger.start", "false");
            SystemProperties.set("persist.vendor.czur.ai.asr.trigger.start", "true");
            action = (mMicStatus == 1) ? TriggerAction.ACTION_OTHER : TriggerAction.ACTION_SELF;
            startRecord();
        }
    }

    public void stopAi() {
        Slog.d(TAG, "===AudioRecord===stopAi======");
        if (!mAsrStarted) return;
        mAsrStarted = false;
        if (!mKwsStarted) {
            SystemProperties.set("persist.vendor.czur.ai.interaction.trigger.stop", "false");
            SystemProperties.set("persist.vendor.czur.ai.asr.trigger.stop", "true");
            stopRecord();
        }
    }

    /**
     * micStatus
     *  -> 1，第三方录音开启，转为OTHER
     *  -> 0, 第三方录音关闭，转为SELF
     */
    public void changeAiStream(int micStatus) {
        Slog.d(TAG, "===AudioRecord===changeAiStream micStatus : " + micStatus);
        mMicStatus = micStatus;
        if (!mKwsStarted && !mAsrStarted) {
            Slog.d(TAG, "===AudioRecord===don't start");
            return;
        }
        TriggerAction startAction = (micStatus == 1) ? TriggerAction.ACTION_OTHER : TriggerAction.ACTION_SELF;

        if (action == startAction) {
            Slog.d(TAG, "===AudioRecord===changeAiStream same action");
            return;
        }
    
        // Stop recording
        SystemProperties.set("persist.vendor.czur.ai.asr.trigger.stop", "true");
        stopRecord();
    
        // Start recording
        action = startAction;
        SystemProperties.set("persist.vendor.czur.ai.asr.trigger.start", "true");
        startRecord();
    }

    public void startInteraction() {
        Slog.d(TAG, "===AudioRecord===startInteraction======mKwsStarted : " + mKwsStarted + ", mMicStatus : " + mMicStatus);
        if (mKwsStarted) {
            Slog.d(TAG, "===AudioRecord===startInteraction======mKwsStarted return");
            return;
        }
        mKwsStarted = true;
        /*if (!mAsrStarted) {
            SystemProperties.set("persist.vendor.czur.ai.interaction.trigger.start", "true");
            action = (mMicStatus == 1) ? TriggerAction.ACTION_OTHER : TriggerAction.ACTION_SELF;
            startRecord();
        }*/
        if (!mAsrStarted && mMicStatus != 1) {
            SystemProperties.set("persist.vendor.czur.ai.interaction.trigger.start", "true");
            action = TriggerAction.ACTION_SELF;
            startRecord();
        }
    }

    public void stopInteraction() {
        Slog.d(TAG, "===AudioRecord===stopInteraction======mKwsStarted : " + mKwsStarted + ", mMicStatus : " + mMicStatus);
        if (!mKwsStarted) {
            Slog.d(TAG, "===AudioRecord===stopInteraction======!mKwsStarted return");
            return;
        }
        mKwsStarted = false;
        if (!mAsrStarted) {
            SystemProperties.set("persist.vendor.czur.ai.interaction.trigger.stop", "true");
            stopRecord();
        }
    }

    // 自主使用AudioRecord开启录音， 并处理音频流
    private void recordBySelf(){
        Slog.d(TAG, "===AudioRecord===recordBySelf======");
        audioRecord.startRecording();
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                handleAudioStream();
                return;
            }
        });
    }
    // 依赖其他第三方App录音，framework层会回调copyMicData
    private void recordByOther(){
        Slog.d(TAG, "===AudioRecord===recordByOther======");
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                dispatchAudioData();
                return;
            }
        });
    }

    /**
     * 暂停录音
     */
    public void pauseRecord() {
        trace("===pauseRecord===");
        if (status != Status.STATUS_START) {
            // throw new IllegalStateException("没有在录音");
            Slog.d(TAG, "没有在录音");
        } else {
            audioRecord.stop();
            status = Status.STATUS_PAUSE;
        }
    }

    /**
     * 停止录音
     */
    public void stopRecord() {

        if (audioRecord == null) {
            Slog.e(TAG, "audioRecord已销毁");
            return;
        }
        
        trace("===AudioRecord======stopRecord===");
        if (status == Status.STATUS_NO_READY || status == Status.STATUS_READY) {
            Slog.e(TAG, "没有在录音");
        } else {
            if (audioRecord != null) {
                audioRecord.stop();
                audioRecord.release();
                audioRecord = null;    
            }
            status = Status.STATUS_STOP;
            ringBuffer.clear();
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        trace("===release===");

        if (audioRecord != null) {
            audioRecord.release();
            audioRecord = null;
        }

        status = Status.STATUS_NO_READY;
    }

    public void saveMicData(byte[] data, int size) {
        try {
            if (status == Status.STATUS_START && action == TriggerAction.ACTION_OTHER) {
                ringBuffer.write(data);
            }
        } catch (InterruptedException e) {
            Slog.e(TAG, "save other process mic data error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理音频流
     *
     */
    private void handleAudioStream() {
        Slog.d(TAG, "======handleAudioStream======");
        byte[] audiodata = new byte[bufferSizeInBytes * 3];
        int readsize = 0;
        FileOutputStream fos = null;
        if (dumpAudioData) {
            try {
                String currentFileName = fileName;
                if (status == Status.STATUS_PAUSE) {
                    //假如是暂停录音 将文件名后面加个数字,防止重名文件内容被覆盖
                    currentFileName += filesName.size();
                }
                filesName.add(currentFileName);
                String filePath = FileUtil.getPcmFileAbsolutePath(currentFileName);
                Slog.d(TAG, "dump save path: " + filePath);
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
                fos = new FileOutputStream(file);
            } catch (IllegalStateException e) {
                Slog.e(TAG, e.getMessage());
                throw new IllegalStateException(e.getMessage());
            } catch (FileNotFoundException e) {
                Slog.e(TAG, e.getMessage());
            }
        }

        //将录音状态设置成正在录音状态
        status = Status.STATUS_START;
        while (status == Status.STATUS_START) {
            readsize = audioRecord.read(audiodata, 0, bufferSizeInBytes * 3);
            if (AudioRecord.ERROR_INVALID_OPERATION != readsize) {
                try {
                    if (dumpAudioData && fos != null) {
                        fos.write(audiodata);
                    }
                } catch (IOException e) {
                    Slog.e(TAG, e.getMessage());
                }

                Slog.d(TAG, "======handleAudioStream======1");
                if (mAsrStarted && translateListener != null) {
                    Slog.d(TAG, "======handleAudioStream======2");
                    translateListener.recordOfByte(audiodata, 0, audiodata.length);
                }
                if (mKwsStarted && interactionListener != null) {
                    //用于小星同学
                    Slog.d(TAG, "======handleAudioStream======3");
                    interactionListener.recordOfByte(audiodata, 0, audiodata.length);
                }
            }
        }

        if (dumpAudioData) {
            try {
                if (fos != null) {
                    fos.close();// 关闭写入流
                }
            } catch (IOException e) {
                Slog.e(TAG, e.getMessage());
            }
        }
    }

    private void dispatchAudioData() {
        Slog.d(TAG, "======dispatchAudioData======");
        FileOutputStream fos = null;
        if (dumpAudioData) {
            try {
                String currentFileName = fileName;
                if (status == Status.STATUS_PAUSE) {
                    //假如是暂停录音 将文件名后面加个数字,防止重名文件内容被覆盖
                    currentFileName += filesName.size();
                }
                filesName.add(currentFileName);
                String filePath = FileUtil.getPcmFileAbsolutePath(currentFileName);
                Slog.d(TAG, "dump save path: " + filePath);
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
                fos = new FileOutputStream(file);
            } catch (IllegalStateException e) {
                Slog.e(TAG, e.getMessage());
                throw new IllegalStateException(e.getMessage());
            } catch (FileNotFoundException e) {
                Slog.e(TAG, e.getMessage());
            }
        }
        status = Status.STATUS_START;
        int chunkSize = getAudioChunkMsSize();
        while (status == Status.STATUS_START) {
            try {
                byte[] audiodata = ringBuffer.read(chunkSize * 3);
                if (dumpAudioData && fos != null) {
                    fos.write(audiodata);
                }
                Slog.d(TAG, "======dispatchAudioData======1");
                if (mAsrStarted && translateListener != null) {
                    Slog.d(TAG, "======dispatchAudioData======2");
                    translateListener.recordOfByte(audiodata, 0, audiodata.length);
                }
                if (mKwsStarted && interactionListener != null) {
                    //用于小星同学
                    Slog.d(TAG, "======dispatchAudioData======3");
                    interactionListener.recordOfByte(audiodata, 0, audiodata.length);
                }
            } catch (IOException | InterruptedException e) {
                Slog.e(TAG, e.getMessage());
            }
        }
        if (dumpAudioData) {
            try {
                if (fos != null) {
                    fos.close(); // 关闭写入流
                }
            } catch (IOException e) {
                Slog.e(TAG, e.getMessage());
            }
        }
    }

    private String getSavePath() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMddHHmmss");
        String stamp = dateFormat.format(date);
        return stamp;
    }

    public Status getStatus() {
        return status;
    }

    public void setTranslateListener(RecordStreamListener translateListener) {
        this.translateListener = translateListener;
    }
    public void setInteractionListener(RecordStreamListener interactionListener) {
        Slog.d(TAG, "======setInteractionListener======");
        this.interactionListener = interactionListener;
    }

    public void sendRecordMsg(int what, int delayMillis) {
        Message msg = mHandler.obtainMessage(what);
        if (delayMillis > 0) {
            mHandler.sendMessageDelayed(msg, delayMillis);
        } else {
            mHandler.sendMessage(msg);
        }
    }

    /**
     * 录音对象的状态
     */
    public enum Status {
        //未开始
        STATUS_NO_READY,
        //预备
        STATUS_READY,
        //录音
        STATUS_START,
        //暂停
        STATUS_PAUSE,
        //停止
        STATUS_STOP
    }

    public enum TriggerAction{
        // by self
        ACTION_SELF,
        // by other
        ACTION_OTHER
    }


    final class RecorderHandler extends Handler {
        static final int AUDIO_START_MSG = 5001;
        static final int AUDIO_STOP_MSG = 5002;

        public RecorderHandler(Looper looper) {
            super(looper, null, true);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AUDIO_START_MSG:
                {
                    if (status != Status.STATUS_START && action == TriggerAction.ACTION_OTHER) {
                        startRecord();
                    }
                }
                break;
                case AUDIO_STOP_MSG:
                {
                    if (status == Status.STATUS_START && action == TriggerAction.ACTION_OTHER) {
                        stopRecord();
                    }
                }
                default:
                    super.handleMessage(msg);
            }
        }
    }
}