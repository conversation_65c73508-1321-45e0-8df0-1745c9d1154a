# Copyright (C) 2012 The Android Open Source Project
#
# USB configuration common for all android devices
#

on post-fs-data
    chown system system /sys/class/android_usb/android0/f_mass_storage/lun/file
    chmod 0660 /sys/class/android_usb/android0/f_mass_storage/lun/file
    chown system system /sys/class/android_usb/android0/f_rndis/ethaddr
    chmod 0660 /sys/class/android_usb/android0/f_rndis/ethaddr
    mkdir /data/misc/adb 02750 system shell
    mkdir /data/adb 0700 root root encryption=Require

# adbd is controlled via property triggers in init.<platform>.usb.rc
service adbd /system/bin/adbd --root_seclabel=u:r:su:s0
    class core
    socket adbd seqpacket 660 system system
    disabled
    updatable
    seclabel u:r:adbd:s0

on property:vendor.sys.usb.adb.disabled=*
    setprop sys.usb.adb.disabled ${vendor.sys.usb.adb.disabled}

# Set default value on sys.usb.configfs early in boot sequence. It will be
# overridden in `on boot` action of init.hardware.rc.
on init
    setprop sys.usb.configfs 0

# Used to disable USB when switching states
on property:sys.usb.config=none && property:sys.usb.configfs=0
    stop adbd
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/bDeviceClass 0
    setprop sys.usb.state ${sys.usb.config}

# adb only USB configuration
# This is the fallback configuration if the
# USB manager fails to set a standard configuration
on property:sys.usb.config=adb && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 4EE7
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    start adbd
    setprop sys.usb.state ${sys.usb.config}

# USB accessory configuration
on property:sys.usb.config=accessory && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d00
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

# USB accessory configuration, with adb
on property:sys.usb.config=accessory,adb && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d01
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    start adbd
    setprop sys.usb.state ${sys.usb.config}

# audio accessory configuration
on property:sys.usb.config=audio_source && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d02
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

# audio accessory configuration, with adb
on property:sys.usb.config=audio_source,adb && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d03
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    start adbd
    setprop sys.usb.state ${sys.usb.config}

# USB and audio accessory configuration
on property:sys.usb.config=accessory,audio_source && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d04
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

# USB and audio accessory configuration, with adb
on property:sys.usb.config=accessory,audio_source,adb && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/enable 0
    write /sys/class/android_usb/android0/idVendor 18d1
    write /sys/class/android_usb/android0/idProduct 2d05
    write /sys/class/android_usb/android0/functions ${sys.usb.config}
    write /sys/class/android_usb/android0/enable 1
    start adbd
    setprop sys.usb.state ${sys.usb.config}

# Used to set USB configuration at boot and to switch the configuration
# when changing the default configuration
# Studio S设备特殊处理：延迟USB配置，确保小圆屏组件优先启动
# Studio S device special handling: Delay USB configuration to ensure small round screen component starts first
on boot && property:persist.sys.usb.config=* && property:persist.vendor.czur.hardware.model!=studios
    setprop sys.usb.config ${persist.sys.usb.config}

# Studio S设备：延迟USB配置，解决USB与小圆屏的时序冲突
# Studio S device: Delay USB configuration to solve USB and small round screen timing conflict
on boot && property:persist.sys.usb.config=* && property:persist.vendor.czur.hardware.model=studios
    # 启动延迟定时器，10秒后配置USB，确保小圆屏组件有足够时间启动
    # Start delay timer, configure USB after 10 seconds to ensure small round screen component has enough time to start
    start studios_usb_delay_timer

# Studio S设备USB延迟配置服务
# Studio S device USB delay configuration service
service studios_usb_delay_timer /system/bin/sleep 10
    class main
    user root
    group root
    oneshot

# 延迟定时器结束后配置USB
# Configure USB after delay timer ends
on property:init.svc.studios_usb_delay_timer=stopped && property:persist.vendor.czur.hardware.model=studios
    setprop sys.usb.config ${persist.sys.usb.config}

#
# USB type C
#

# USB mode changes
on property:sys.usb.typec.mode=dfp
    write /sys/class/dual_role_usb/otg_default/mode ${sys.usb.typec.mode}
    setprop sys.usb.typec.state ${sys.usb.typec.mode}

on property:sys.usb.typec.mode=ufp
    write /sys/class/dual_role_usb/otg_default/mode ${sys.usb.typec.mode}
    setprop sys.usb.typec.state ${sys.usb.typec.mode}

# USB data role changes
on property:sys.usb.typec.data_role=device
    write /sys/class/dual_role_usb/otg_default/data_role ${sys.usb.typec.data_role}
    setprop sys.usb.typec.state ${sys.usb.typec.data_role}

on property:sys.usb.typec.data_role=host
    write /sys/class/dual_role_usb/otg_default/data_role ${sys.usb.typec.data_role}
    setprop sys.usb.typec.state ${sys.usb.typec.data_role}

# USB power role changes
on property:sys.usb.typec.power_role=source
    write /sys/class/dual_role_usb/otg_default/power_role ${sys.usb.typec.power_role}
    setprop sys.usb.typec.state ${sys.usb.typec.power_role}

on property:sys.usb.typec.power_role=sink
    write /sys/class/dual_role_usb/otg_default/power_role ${sys.usb.typec.power_role}
    setprop sys.usb.typec.state ${sys.usb.typec.power_role}

on userspace-reboot-requested
  setprop sys.usb.config ""
  setprop sys.usb.state ""
