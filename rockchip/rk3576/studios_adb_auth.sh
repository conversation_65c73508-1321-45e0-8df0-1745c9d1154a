#!/system/bin/sh

# Studio S设备ADB授权辅助脚本
# Studio S device ADB authorization helper script

echo "Studio S ADB Authorization Helper"
echo "================================="

# 检查设备型号
HARDWARE_MODEL=$(getprop persist.vendor.czur.hardware.model)
if [ "$HARDWARE_MODEL" != "studios" ]; then
    echo "Error: This script is only for Studio S devices"
    exit 1
fi

# 检查是否为调试版本
DEBUGGABLE=$(getprop ro.debuggable)
if [ "$DEBUGGABLE" != "true" ]; then
    echo "Error: This script only works on debuggable builds"
    exit 1
fi

# 检查启动状态
BOOT_COMPLETED=$(getprop sys.boot_completed)
if [ "$BOOT_COMPLETED" != "1" ]; then
    echo "Error: System boot not completed yet"
    exit 1
fi

# 检查是否有待授权的ADB连接
PENDING_KEY=$(getprop persist.sys.adb.pending_key)
PENDING_FINGERPRINTS=$(getprop persist.sys.adb.pending_fingerprints)

if [ -z "$PENDING_KEY" ] || [ -z "$PENDING_FINGERPRINTS" ]; then
    echo "No pending ADB authorization found."
    echo ""
    echo "To use this script:"
    echo "1. Connect USB cable to your computer"
    echo "2. Wait for system to boot completely"
    echo "3. Run this script again"
    exit 0
fi

echo "Found pending ADB authorization:"
echo "Fingerprints: $PENDING_FINGERPRINTS"
echo ""
echo "Options:"
echo "1. Allow this connection (temporary)"
echo "2. Allow this connection (always)"
echo "3. Deny this connection"
echo "4. Cancel"
echo ""
echo -n "Please choose (1-4): "

# 由于shell脚本限制，这里只提供基本的授权功能
# 实际的对话框会在Java代码中处理
echo ""
echo "Note: The authorization dialog will appear automatically."
echo "If you don't see it, please check the main screen."

# 触发授权检查
am broadcast -a com.android.server.adb.CHECK_PENDING_AUTH

exit 0
